"""
Fast Model Comparison - Optimized for Speed
Excludes slow models like SVM and focuses on efficient algorithms
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

# Fast models only
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB

# Advanced fast models
try:
    import xgboost as xgb
    print("✅ XGBoost available")
except ImportError:
    print("❌ XGBoost not available - install with: pip install xgboost")
    xgb = None

try:
    import lightgbm as lgb
    print("✅ LightGBM available")
except ImportError:
    print("❌ LightGBM not available - install with: pip install lightgbm")
    lgb = None

def main():
    print("🚀 FAST MODEL COMPARISON FOR CREDIT RISK")
    print("=" * 60)
    print("⚡ Optimized for speed - SVM and other slow models excluded")
    
    # Load data
    print("\n📂 Loading data...")
    df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
    
    # Prepare features
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    feature_columns = [col for col in numerical_cols if col not in ['cin']]
    
    X = df[feature_columns].fillna(0)
    y = df['decision_finale']
    
    # Encode target
    target_encoder = LabelEncoder()
    y_encoded = target_encoder.fit_transform(y)
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
    )
    
    print(f"✅ Data prepared: {X_train.shape[0]} train, {X_test.shape[0]} test samples")
    
    # Setup fast models
    print("\n🤖 Setting up FAST models...")
    models = {}
    
    # Fast and efficient models
    models['Logistic Regression'] = LogisticRegression(random_state=42, max_iter=1000, class_weight='balanced')
    models['Random Forest'] = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced')
    models['Extra Trees'] = ExtraTreesClassifier(n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced')
    models['Naive Bayes'] = GaussianNB()
    
    # Advanced models (if available)
    if xgb is not None:
        models['XGBoost'] = xgb.XGBClassifier(random_state=42, eval_metric='logloss', n_jobs=-1, n_estimators=100)
    
    if lgb is not None:
        models['LightGBM'] = lgb.LGBMClassifier(random_state=42, n_jobs=-1, verbose=-1, n_estimators=100)
    
    print(f"✅ {len(models)} fast models configured")
    
    # Train and evaluate
    print("\n🏋️ Training models...")
    results = []
    
    for model_name, model in models.items():
        print(f"\n🔄 Training {model_name}...")
        
        try:
            import time
            start_time = time.time()
            
            # Train
            model.fit(X_train, y_train)
            training_time = time.time() - start_time
            
            # Predict
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
            
            # Metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # ROC-AUC
            if y_pred_proba is not None:
                if len(np.unique(y_test)) > 2:
                    roc_auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')
                else:
                    roc_auc = roc_auc_score(y_test, y_pred_proba[:, 1])
            else:
                roc_auc = np.nan
            
            results.append({
                'Model': model_name,
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'ROC-AUC': roc_auc,
                'Training_Time': training_time
            })
            
            print(f"   ✅ Completed in {training_time:.1f}s | F1: {f1:.4f} | Acc: {accuracy:.4f}")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            continue
    
    # Results
    print("\n📊 RESULTS SUMMARY")
    print("=" * 50)
    
    results_df = pd.DataFrame(results)
    results_df = results_df.round(4)
    
    # Sort by F1-Score
    results_df = results_df.sort_values('F1-Score', ascending=False)
    
    print("\n🏆 MODEL RANKINGS:")
    for i, (_, row) in enumerate(results_df.iterrows(), 1):
        medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "  "
        print(f"{medal} {i}. {row['Model']:<20} F1: {row['F1-Score']:.4f} | Acc: {row['Accuracy']:.4f} | Time: {row['Training_Time']:.1f}s")
    
    # Best model
    best_model_name = results_df.iloc[0]['Model']
    best_f1 = results_df.iloc[0]['F1-Score']
    
    print(f"\n🎯 RECOMMENDED MODEL: {best_model_name}")
    print(f"📊 Best F1-Score: {best_f1:.4f}")
    
    # Quick visualization
    plt.figure(figsize=(12, 6))
    
    # F1-Score comparison
    plt.subplot(1, 2, 1)
    bars = plt.bar(range(len(results_df)), results_df['F1-Score'], 
                   color=['gold' if i == 0 else 'silver' if i == 1 else 'chocolate' if i == 2 else 'lightblue' 
                          for i in range(len(results_df))])
    plt.xticks(range(len(results_df)), results_df['Model'], rotation=45, ha='right')
    plt.ylabel('F1-Score')
    plt.title('Model Performance (F1-Score)')
    plt.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars, results_df['F1-Score']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')
    
    # Training time comparison
    plt.subplot(1, 2, 2)
    plt.bar(range(len(results_df)), results_df['Training_Time'], color='lightcoral')
    plt.xticks(range(len(results_df)), results_df['Model'], rotation=45, ha='right')
    plt.ylabel('Training Time (seconds)')
    plt.title('Training Time Comparison')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Save results
    results_df.to_csv('fast_model_comparison_results.csv', index=False)
    print(f"\n💾 Results saved to: fast_model_comparison_results.csv")
    
    # Save best model
    best_model = models[best_model_name]
    import joblib
    joblib.dump(best_model, f'best_fast_model_{best_model_name.lower().replace(" ", "_")}.pkl')
    print(f"💾 Best model saved: best_fast_model_{best_model_name.lower().replace(' ', '_')}.pkl")
    
    print(f"\n🎉 FAST MODEL COMPARISON COMPLETED!")
    print(f"⏱️ Total time saved by excluding SVM: ~30+ minutes")
    
    return results_df

if __name__ == "__main__":
    results = main()
