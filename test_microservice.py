"""
Test script for Credit Risk Microservice API
"""

import requests
import json
import time
from typing import Dict, Any

# API Configuration
BASE_URL = "http://localhost:8000"
AUTH_TOKEN = "demo-token-123"
HEADERS = {
    "Authorization": f"Bearer {AUTH_TOKEN}",
    "Content-Type": "application/json"
}

def test_health_check():
    """Test health check endpoint"""
    print("🔍 Testing Health Check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def test_model_info():
    """Test model info endpoint"""
    print("\n🔍 Testing Model Info...")
    response = requests.get(f"{BASE_URL}/model/info", headers=HEADERS)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Model Type: {data['model_metadata']['model_type']}")
        print(f"Features Count: {data['model_metadata']['features_count']}")
        print(f"Target Classes: {data['target_classes']}")
    return response.status_code == 200

def test_single_prediction():
    """Test single prediction endpoint"""
    print("\n🔍 Testing Single Prediction...")
    
    # High approval probability case
    test_application = {
        "age": 35,
        "nombre_enfants": 2,
        "revenu_mensuel": 4000,
        "montant_demande": 30000,
        "duree_demande": 60,
        "dette_totale": 5000,
        "anciennete_emploi": 8,
        "patrimoine_total": 150000,
        "valeur_immobilier": 120000,
        "valeur_vehicule": 25000,
        "epargne": 15000,
        "score_comportement": 0.95,
        "nombre_credits_anterieurs": 2,
        "retard_maximum_jours": 0,
        "nombre_incidents_12m": 0,
        "nombre_rejets_12m": 0,
        "anciennete_relation_bancaire": 10,
        "autres_revenus": 500,
        "apport_personnel": 10000,
        "taux_chomage_sectoriel": 0.08
    }
    
    response = requests.post(
        f"{BASE_URL}/predict",
        headers=HEADERS,
        json=test_application
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Decision: {result['decision']}")
        print(f"Confidence: {result['confidence']:.3f}")
        print(f"Risk Score: {result['risk_score']:.3f}")
        print(f"Recommendation: {result['recommendation']}")
        print("Probabilities:")
        for decision, prob in result['probabilities'].items():
            print(f"  {decision}: {prob:.3f}")
    else:
        print(f"Error: {response.text}")
    
    return response.status_code == 200

def test_rejection_case():
    """Test high rejection probability case"""
    print("\n🔍 Testing Rejection Case...")
    
    # High rejection probability case
    test_application = {
        "age": 25,
        "nombre_enfants": 0,
        "revenu_mensuel": 800,
        "montant_demande": 100000,
        "duree_demande": 120,
        "dette_totale": 50000,
        "anciennete_emploi": 1,
        "patrimoine_total": 10000,
        "valeur_immobilier": 0,
        "valeur_vehicule": 5000,
        "epargne": 1000,
        "score_comportement": 0.3,
        "nombre_credits_anterieurs": 8,
        "retard_maximum_jours": 90,
        "nombre_incidents_12m": 5,
        "nombre_rejets_12m": 3,
        "anciennete_relation_bancaire": 2,
        "autres_revenus": 0,
        "apport_personnel": 0,
        "taux_chomage_sectoriel": 0.15
    }
    
    response = requests.post(
        f"{BASE_URL}/predict",
        headers=HEADERS,
        json=test_application
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Decision: {result['decision']}")
        print(f"Confidence: {result['confidence']:.3f}")
        print(f"Risk Score: {result['risk_score']:.3f}")
    
    return response.status_code == 200

def test_batch_prediction():
    """Test batch prediction endpoint"""
    print("\n🔍 Testing Batch Prediction...")
    
    applications = [
        {
            "age": 30,
            "revenu_mensuel": 3000,
            "montant_demande": 25000,
            "duree_demande": 48,
            "dette_totale": 8000,
            "anciennete_emploi": 5,
            "patrimoine_total": 80000,
            "score_comportement": 0.8,
            "nombre_credits_anterieurs": 1,
            "retard_maximum_jours": 0
        },
        {
            "age": 45,
            "revenu_mensuel": 2000,
            "montant_demande": 50000,
            "duree_demande": 72,
            "dette_totale": 20000,
            "anciennete_emploi": 15,
            "patrimoine_total": 120000,
            "score_comportement": 0.7,
            "nombre_credits_anterieurs": 3,
            "retard_maximum_jours": 15
        }
    ]
    
    response = requests.post(
        f"{BASE_URL}/batch-predict",
        headers=HEADERS,
        json=applications
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Total Processed: {result['total_processed']}")
        for item in result['batch_results']:
            if 'prediction' in item:
                pred = item['prediction']
                print(f"App {item['application_id']}: {pred['decision']} (conf: {pred['confidence']:.3f})")
            else:
                print(f"App {item['application_id']}: Error - {item['error']}")
    
    return response.status_code == 200

def test_authentication():
    """Test authentication"""
    print("\n🔍 Testing Authentication...")
    
    # Test without token
    response = requests.post(f"{BASE_URL}/predict", json={})
    print(f"Without token - Status: {response.status_code}")
    
    # Test with wrong token
    wrong_headers = {"Authorization": "Bearer wrong-token", "Content-Type": "application/json"}
    response = requests.post(f"{BASE_URL}/predict", headers=wrong_headers, json={})
    print(f"Wrong token - Status: {response.status_code}")
    
    return True

def test_validation():
    """Test input validation"""
    print("\n🔍 Testing Input Validation...")
    
    # Invalid age
    invalid_app = {
        "age": 150,  # Invalid age
        "revenu_mensuel": 2000,
        "montant_demande": 25000,
        "duree_demande": 48
    }
    
    response = requests.post(
        f"{BASE_URL}/predict",
        headers=HEADERS,
        json=invalid_app
    )
    
    print(f"Invalid age - Status: {response.status_code}")
    if response.status_code == 422:
        print("✅ Validation working correctly")
    
    return True

def performance_test():
    """Basic performance test"""
    print("\n🔍 Performance Test...")
    
    test_app = {
        "age": 35,
        "revenu_mensuel": 3000,
        "montant_demande": 30000,
        "duree_demande": 60,
        "dette_totale": 10000,
        "anciennete_emploi": 5,
        "patrimoine_total": 100000,
        "score_comportement": 0.8,
        "nombre_credits_anterieurs": 2,
        "retard_maximum_jours": 0
    }
    
    # Test 10 requests
    times = []
    for i in range(10):
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/predict",
            headers=HEADERS,
            json=test_app
        )
        end_time = time.time()
        
        if response.status_code == 200:
            times.append(end_time - start_time)
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"Average response time: {avg_time:.3f} seconds")
        print(f"Min: {min(times):.3f}s, Max: {max(times):.3f}s")
    
    return True

def main():
    """Run all tests"""
    print("🚀 CREDIT RISK MICROSERVICE API TESTS")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Model Info", test_model_info),
        ("Single Prediction", test_single_prediction),
        ("Rejection Case", test_rejection_case),
        ("Batch Prediction", test_batch_prediction),
        ("Authentication", test_authentication),
        ("Input Validation", test_validation),
        ("Performance", performance_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, "✅ PASS" if success else "❌ FAIL"))
        except Exception as e:
            results.append((test_name, f"❌ ERROR: {str(e)}"))
    
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    for test_name, result in results:
        print(f"{test_name:<20}: {result}")
    
    passed = sum(1 for _, result in results if "PASS" in result)
    total = len(results)
    print(f"\n🎯 Overall: {passed}/{total} tests passed")

if __name__ == "__main__":
    main()
