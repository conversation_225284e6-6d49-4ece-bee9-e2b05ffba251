{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "title",
   "metadata": {},
   "source": [
    "# Tunisian Credit Dataset - EDA & Feature Engineering\n",
    "\n",
    "This notebook provides comprehensive **Exploratory Data Analysis (EDA)** and **Feature Engineering** for the cleaned Tunisian credit dataset.\n",
    "\n",
    "## 📋 Objectives:\n",
    "1. **📊 Exploratory Data Analysis**: Understand data patterns and relationships\n",
    "2. **🎯 Target Variable Analysis**: Deep dive into credit decisions\n",
    "3. **🔗 Feature Correlation Analysis**: Identify relationships and multicollinearity\n",
    "4. **🔧 Feature Engineering**: Create new meaningful features\n",
    "5. **⚡ Feature Selection**: Identify most important predictors\n",
    "6. **🚀 Model Preparation**: Prepare data for machine learning"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import necessary libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import plotly.express as px\n",
    "import plotly.graph_objects as go\n",
    "from plotly.subplots import make_subplots\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Statistical libraries\n",
    "from scipy import stats\n",
    "from scipy.stats import chi2_contingency, pearsonr, spearmanr\n",
    "from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder\n",
    "from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif\n",
    "from sklearn.ensemble import RandomForestClassifier\n",
    "from sklearn.model_selection import train_test_split\n",
    "\n",
    "# Set style and options\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.width', None)\n",
    "\n",
    "print(\"📚 All libraries imported successfully!\")\n",
    "print(\"🎨 Visualization settings configured!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "load-data",
   "metadata": {},
   "source": [
    "## 📂 Load Cleaned Dataset"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "load-cleaned-data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the cleaned dataset\n",
    "try:\n",
    "    # Try loading pickle first (faster)\n",
    "    df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')\n",
    "    print(\"✅ Loaded from pickle file (faster)\")\n",
    "except:\n",
    "    # Fallback to CSV\n",
    "    df = pd.read_csv('tunisian_credit_dataset_cleaned.csv')\n",
    "    print(\"✅ Loaded from CSV file\")\n",
    "\n",
    "print(f\"📊 Dataset shape: {df.shape}\")\n",
    "print(f\"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n",
    "print(f\"🎯 Target variable: decision_finale\")\n",
    "\n",
    "# Quick overview\n",
    "print(\"\\n📋 Dataset Overview:\")\n",
    "df.info()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "eda-section",
   "metadata": {},
   "source": [
    "# 📊 EXPLORATORY DATA ANALYSIS (EDA)\n",
    "\n",
    "## 1. Target Variable Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "target-analysis",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyze target variable distribution\n",
    "print(\"🎯 TARGET VARIABLE ANALYSIS: decision_finale\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "target_counts = df['decision_finale'].value_counts()\n",
    "target_pct = (target_counts / len(df) * 100).round(2)\n",
    "\n",
    "print(\"Distribution:\")\n",
    "for decision, count in target_counts.items():\n",
    "    pct = target_pct[decision]\n",
    "    print(f\"  • {decision}: {count:,} ({pct}%)\")\n",
    "\n",
    "# Create visualization\n",
    "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n",
    "\n",
    "# Bar plot\n",
    "target_counts.plot(kind='bar', ax=axes[0], color=['#2E8B57', '#DC143C', '#FF8C00'])\n",
    "axes[0].set_title('Credit Decision Distribution', fontsize=14, fontweight='bold')\n",
    "axes[0].set_xlabel('Decision')\n",
    "axes[0].set_ylabel('Count')\n",
    "axes[0].tick_params(axis='x', rotation=45)\n",
    "\n",
    "# Pie chart\n",
    "axes[1].pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%', \n",
    "           colors=['#2E8B57', '#DC143C', '#FF8C00'], startangle=90)\n",
    "axes[1].set_title('Credit Decision Proportions', fontsize=14, fontweight='bold')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Check for class imbalance\n",
    "min_class_pct = target_pct.min()\n",
    "max_class_pct = target_pct.max()\n",
    "imbalance_ratio = max_class_pct / min_class_pct\n",
    "\n",
    "print(f\"\\n⚖️ Class Balance Analysis:\")\n",
    "print(f\"   Imbalance ratio: {imbalance_ratio:.2f}:1\")\n",
    "if imbalance_ratio > 3:\n",
    "    print(\"   ⚠️ Significant class imbalance detected - consider balancing techniques\")\n",
    "else:\n",
    "    print(\"   ✅ Classes are reasonably balanced\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "univariate-analysis",
   "metadata": {},
   "source": [
    "## 2. Univariate Analysis - Numerical Features"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "numerical-univariate",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Identify numerical columns\n",
    "numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n",
    "print(f\"📊 Found {len(numerical_cols)} numerical columns\")\n",
    "\n",
    "# Key financial metrics for detailed analysis\n",
    "key_financial_cols = [\n",
    "    'age', 'revenu_mensuel', 'revenu_total', 'montant_demande', \n",
    "    'dette_totale', 'ratio_endettement', 'score_pd', 'score_comportement'\n",
    "]\n",
    "\n",
    "# Filter to existing columns\n",
    "key_financial_cols = [col for col in key_financial_cols if col in numerical_cols]\n",
    "\n",
    "print(f\"\\n🔍 Analyzing {len(key_financial_cols)} key financial metrics:\")\n",
    "for col in key_financial_cols:\n",
    "    print(f\"  • {col}\")\n",
    "\n",
    "# Create distribution plots for key financial metrics\n",
    "n_cols = 4\n",
    "n_rows = (len(key_financial_cols) + n_cols - 1) // n_cols\n",
    "\n",
    "fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n",
    "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n",
    "\n",
    "for i, col in enumerate(key_financial_cols):\n",
    "    if i < len(axes):\n",
    "        # Histogram with KDE\n",
    "        df[col].hist(bins=50, alpha=0.7, ax=axes[i], color='skyblue', edgecolor='black')\n",
    "        \n",
    "        # Add statistics\n",
    "        mean_val = df[col].mean()\n",
    "        median_val = df[col].median()\n",
    "        std_val = df[col].std()\n",
    "        \n",
    "        axes[i].axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.2f}')\n",
    "        axes[i].axvline(median_val, color='green', linestyle='--', label=f'Median: {median_val:.2f}')\n",
    "        \n",
    "        axes[i].set_title(f'{col}\\n(μ={mean_val:.2f}, σ={std_val:.2f})', fontweight='bold')\n",
    "        axes[i].set_xlabel(col)\n",
    "        axes[i].set_ylabel('Frequency')\n",
    "        axes[i].legend()\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "\n",
    "# Hide empty subplots\n",
    "for i in range(len(key_financial_cols), len(axes)):\n",
    "    axes[i].set_visible(False)\n",
    "\n",
    "plt.suptitle('Distribution of Key Financial Metrics', fontsize=16, fontweight='bold', y=1.02)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Summary statistics\n",
    "print(\"\\n📈 SUMMARY STATISTICS:\")\n",
    "print(\"=\" * 60)\n",
    "summary_stats = df[key_financial_cols].describe()\n",
    "display(summary_stats)"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "categorical-analysis",
   "metadata": {},
   "source": [
    "## 3. Univariate Analysis - Categorical Features"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "categorical-univariate",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Identify categorical columns\n",
    "categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()\n",
    "print(f\"📊 Found {len(categorical_cols)} categorical columns\")\n",
    "\n",
    "# Key categorical variables for analysis\n",
    "key_categorical_cols = [\n",
    "    'sexe', 'situation_familiale', 'niveau_education', 'region',\n",
    "    'type_contrat', 'type_logement', 'banque_principale', 'type_credit', 'classe_risque'\n",
    "]\n",
    "\n",
    "# Filter to existing columns\n",
    "key_categorical_cols = [col for col in key_categorical_cols if col in categorical_cols]\n",
    "\n",
    "print(f\"\\n🔍 Analyzing {len(key_categorical_cols)} key categorical variables:\")\n",
    "for col in key_categorical_cols:\n",
    "    print(f\"  • {col}\")\n",
    "\n",
    "# Create bar plots for categorical variables\n",
    "n_cols = 3\n",
    "n_rows = (len(key_categorical_cols) + n_cols - 1) // n_cols\n",
    "\n",
    "fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))\n",
    "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n",
    "\n",
    "for i, col in enumerate(key_categorical_cols):\n",
    "    if i < len(axes):\n",
    "        value_counts = df[col].value_counts().head(10)  # Top 10 values\n",
    "        \n",
    "        # Create bar plot\n",
    "        bars = axes[i].bar(range(len(value_counts)), value_counts.values, \n",
    "                          color=plt.cm.Set3(np.linspace(0, 1, len(value_counts))))\n",
    "        \n",
    "        axes[i].set_title(f'{col}\\n({df[col].nunique()} unique values)', fontweight='bold')\n",
    "        axes[i].set_xlabel('Categories')\n",
    "        axes[i].set_ylabel('Count')\n",
    "        axes[i].set_xticks(range(len(value_counts)))\n",
    "        axes[i].set_xticklabels(value_counts.index, rotation=45, ha='right')\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Add value labels on bars\n",
    "        for bar, value in zip(bars, value_counts.values):\n",
    "            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,\n",
    "                        f'{value:,}', ha='center', va='bottom', fontsize=8)\n",
    "\n",
    "# Hide empty subplots\n",
    "for i in range(len(key_categorical_cols), len(axes)):\n",
    "    axes[i].set_visible(False)\n",
    "\n",
    "plt.suptitle('Distribution of Key Categorical Variables', fontsize=16, fontweight='bold', y=1.02)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Detailed breakdown for each categorical variable\n",
    "print(\"\\n📊 CATEGORICAL VARIABLES BREAKDOWN:\")\n",
    "print(\"=\" * 60)\n",
    "for col in key_categorical_cols:\n",
    "    print(f\"\\n🔹 {col.upper()}:\")\n",
    "    value_counts = df[col].value_counts()\n",
    "    percentages = (value_counts / len(df) * 100).round(2)\n",
    "    \n",
    "    for value, count in value_counts.head(5).items():  # Top 5 values\n",
    "        pct = percentages[value]\n",
    "        print(f\"   • {value}: {count:,} ({pct}%)\")\n",
    "    \n",
    "    if len(value_counts) > 5:\n",
    "        others_count = value_counts.iloc[5:].sum()\n",
    "        others_pct = (others_count / len(df) * 100).round(2)\n",
    "        print(f\"   • Others ({len(value_counts)-5} categories): {others_count:,} ({others_pct}%)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "bivariate-analysis",
   "metadata": {},
   "source": [
    "## 4. Bivariate Analysis - Target vs Features"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "target-vs-numerical",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyze relationship between target and numerical features\n",
    "print(\"🎯 TARGET vs NUMERICAL FEATURES ANALYSIS\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Select key numerical features for analysis\n",
    "key_num_features = [\n",
    "    'age', 'revenu_mensuel', 'montant_demande', 'dette_totale', \n",
    "    'ratio_endettement', 'score_pd', 'score_comportement'\n",
    "]\n",
    "key_num_features = [col for col in key_num_features if col in numerical_cols]\n",
    "\n",
    "# Create box plots\n",
    "n_cols = 3\n",
    "n_rows = (len(key_num_features) + n_cols - 1) // n_cols\n",
    "\n",
    "fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))\n",
    "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n",
    "\n",
    "for i, col in enumerate(key_num_features):\n",
    "    if i < len(axes):\n",
    "        # Create box plot\n",
    "        df.boxplot(column=col, by='decision_finale', ax=axes[i])\n",
    "        axes[i].set_title(f'{col} by Credit Decision')\n",
    "        axes[i].set_xlabel('Credit Decision')\n",
    "        axes[i].set_ylabel(col)\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "\n",
    "# Hide empty subplots\n",
    "for i in range(len(key_num_features), len(axes)):\n",
    "    axes[i].set_visible(False)\n",
    "\n",
    "plt.suptitle('Numerical Features by Credit Decision', fontsize=16, fontweight='bold', y=1.02)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Statistical analysis\n",
    "print(\"\\n📊 STATISTICAL ANALYSIS BY DECISION:\")\n",
    "print(\"=\" * 60)\n",
    "for col in key_num_features:\n",
    "    print(f\"\\n🔹 {col.upper()}:\")\n",
    "    decision_groups = df.groupby('decision_finale')[col].agg(['mean', 'median', 'std']).round(2)\n",
    "    display(decision_groups)\n",
    "    \n",
    "    # ANOVA test\n",
    "    groups = [df[df['decision_finale'] == decision][col].dropna() \n",
    "              for decision in df['decision_finale'].unique()]\n",
    "    \n",
    "    if len(groups) > 1 and all(len(group) > 0 for group in groups):\n",
    "        f_stat, p_value = stats.f_oneway(*groups)\n",
    "        print(f\"   ANOVA: F={f_stat:.3f}, p-value={p_value:.3f}\")\n",
    "        if p_value < 0.05:\n",
    "            print(f\"   ✅ Significant difference between groups (p < 0.05)\")\n",
    "        else:\n",
    "            print(f\"   ❌ No significant difference between groups (p >= 0.05)\")"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "correlation-analysis",
   "metadata": {},
   "source": [
    "## 5. Feature Correlation Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "correlation-heatmap",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Correlation analysis for numerical features\n",
    "print(\"🔗 FEATURE CORRELATION ANALYSIS\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Calculate correlation matrix\n",
    "correlation_matrix = df[numerical_cols].corr()\n",
    "\n",
    "# Create correlation heatmap\n",
    "plt.figure(figsize=(16, 12))\n",
    "mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # Mask upper triangle\n",
    "\n",
    "sns.heatmap(correlation_matrix, \n",
    "            mask=mask,\n",
    "            annot=True, \n",
    "            cmap='RdYlBu_r', \n",
    "            center=0,\n",
    "            square=True,\n",
    "            fmt='.2f',\n",
    "            cbar_kws={\"shrink\": .8})\n",
    "\n",
    "plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold', pad=20)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Identify highly correlated features\n",
    "print(\"\\n🔍 HIGH CORRELATION PAIRS (|r| > 0.7):\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "high_corr_pairs = []\n",
    "for i in range(len(correlation_matrix.columns)):\n",
    "    for j in range(i+1, len(correlation_matrix.columns)):\n",
    "        corr_value = correlation_matrix.iloc[i, j]\n",
    "        if abs(corr_value) > 0.7:\n",
    "            high_corr_pairs.append({\n",
    "                'Feature_1': correlation_matrix.columns[i],\n",
    "                'Feature_2': correlation_matrix.columns[j],\n",
    "                'Correlation': round(corr_value, 3)\n",
    "            })\n",
    "\n",
    "if high_corr_pairs:\n",
    "    high_corr_df = pd.DataFrame(high_corr_pairs)\n",
    "    high_corr_df = high_corr_df.sort_values('Correlation', key=abs, ascending=False)\n",
    "    display(high_corr_df)\n",
    "    print(f\"\\n⚠️ Found {len(high_corr_pairs)} highly correlated pairs - consider for multicollinearity\")\n",
    "else:\n",
    "    print(\"✅ No highly correlated pairs found (|r| > 0.7)\")\n",
    "\n",
    "# Top correlations with specific features of interest\n",
    "if 'score_pd' in numerical_cols:\n",
    "    print(\"\\n🎯 TOP CORRELATIONS WITH PROBABILITY OF DEFAULT (score_pd):\")\n",
    "    print(\"=\" * 60)\n",
    "    pd_correlations = correlation_matrix['score_pd'].abs().sort_values(ascending=False)\n",
    "    for feature, corr in pd_correlations.head(10).items():\n",
    "        if feature != 'score_pd':\n",
    "            print(f\"   • {feature}: {corr:.3f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "feature-engineering-section",
   "metadata": {},
   "source": [
    "# 🔧 FEATURE ENGINEERING\n",
    "\n",
    "## 6. Create New Derived Features"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "feature-engineering",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a copy for feature engineering\n",
    "df_fe = df.copy()\n",
    "\n",
    "print(\"🔧 FEATURE ENGINEERING - CREATING NEW FEATURES\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# 1. Financial Ratios and Derived Features\n",
    "print(\"\\n💰 Creating Financial Ratio Features:\")\n",
    "\n",
    "# Income-based ratios\n",
    "if 'revenu_mensuel' in df_fe.columns and 'montant_demande' in df_fe.columns:\n",
    "    df_fe['loan_to_income_ratio'] = df_fe['montant_demande'] / (df_fe['revenu_mensuel'] * 12)\n",
    "    print(\"   ✅ loan_to_income_ratio\")\n",
    "\n",
    "if 'dette_totale' in df_fe.columns and 'revenu_total' in df_fe.columns:\n",
    "    df_fe['debt_to_income_ratio'] = df_fe['dette_totale'] / (df_fe['revenu_total'] * 12)\n",
    "    print(\"   ✅ debt_to_income_ratio\")\n",
    "\n",
    "# Asset-based ratios\n",
    "if 'patrimoine_total' in df_fe.columns and 'dette_totale' in df_fe.columns:\n",
    "    df_fe['net_worth'] = df_fe['patrimoine_total'] - df_fe['dette_totale']\n",
    "    df_fe['asset_to_debt_ratio'] = df_fe['patrimoine_total'] / (df_fe['dette_totale'] + 1)  # +1 to avoid division by zero\n",
    "    print(\"   ✅ net_worth\")\n",
    "    print(\"   ✅ asset_to_debt_ratio\")\n",
    "\n",
    "# Credit utilization and behavior\n",
    "if 'score_comportement' in df_fe.columns and 'nombre_credits_anterieurs' in df_fe.columns:\n",
    "    df_fe['avg_behavior_per_credit'] = df_fe['score_comportement'] / (df_fe['nombre_credits_anterieurs'] + 1)\n",
    "    print(\"   ✅ avg_behavior_per_credit\")\n",
    "\n",
    "# 2. Age-based features\n",
    "print(\"\\n👤 Creating Age-based Features:\")\n",
    "if 'age' in df_fe.columns:\n",
    "    df_fe['age_group'] = pd.cut(df_fe['age'], \n",
    "                               bins=[0, 25, 35, 45, 55, 100], \n",
    "                               labels=['Young', 'Adult', 'Middle', 'Senior', 'Elder'])\n",
    "    \n",
    "    # Age-income interaction\n",
    "    if 'revenu_mensuel' in df_fe.columns:\n",
    "        df_fe['income_per_age'] = df_fe['revenu_mensuel'] / df_fe['age']\n",
    "        print(\"   ✅ income_per_age\")\n",
    "    \n",
    "    print(\"   ✅ age_group\")\n",
    "\n",
    "# 3. Risk-based features\n",
    "print(\"\\n⚠️ Creating Risk-based Features:\")\n",
    "\n",
    "# Risk score combinations\n",
    "risk_scores = ['score_pd', 'score_lgd', 'score_ead']\n",
    "available_risk_scores = [col for col in risk_scores if col in df_fe.columns]\n",
    "\n",
    "if len(available_risk_scores) >= 2:\n",
    "    df_fe['combined_risk_score'] = df_fe[available_risk_scores].mean(axis=1)\n",
    "    print(\"   ✅ combined_risk_score\")\n",
    "\n",
    "# Credit history features\n",
    "if 'nombre_incidents_12m' in df_fe.columns and 'nombre_rejets_12m' in df_fe.columns:\n",
    "    df_fe['total_negative_events'] = df_fe['nombre_incidents_12m'] + df_fe['nombre_rejets_12m']\n",
    "    print(\"   ✅ total_negative_events\")\n",
    "\n",
    "# 4. Loan-specific features\n",
    "print(\"\\n💳 Creating Loan-specific Features:\")\n",
    "\n",
    "if 'montant_demande' in df_fe.columns and 'duree_demande' in df_fe.columns:\n",
    "    df_fe['monthly_payment_estimate'] = df_fe['montant_demande'] / df_fe['duree_demande']\n",
    "    print(\"   ✅ monthly_payment_estimate\")\n",
    "\n",
    "if 'apport_personnel' in df_fe.columns and 'montant_demande' in df_fe.columns:\n",
    "    df_fe['down_payment_ratio'] = df_fe['apport_personnel'] / df_fe['montant_demande']\n",
    "    print(\"   ✅ down_payment_ratio\")\n",
    "\n",
    "# 5. Employment stability features\n",
    "print(\"\\n💼 Creating Employment Features:\")\n",
    "\n",
    "if 'anciennete_emploi' in df_fe.columns and 'age' in df_fe.columns:\n",
    "    df_fe['employment_stability'] = df_fe['anciennete_emploi'] / df_fe['age']\n",
    "    print(\"   ✅ employment_stability\")\n",
    "\n",
    "print(f\"\\n✅ Feature engineering completed!\")\n",
    "print(f\"📊 Original features: {len(df.columns)}\")\n",
    "print(f\"📊 New features: {len(df_fe.columns)}\")\n",
    "print(f\"📊 Added features: {len(df_fe.columns) - len(df.columns)}\")"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "feature-selection",
   "metadata": {},
   "source": [
    "## 7. Feature Importance & Selection"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "feature-importance",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature importance analysis\n",
    "print(\"⚡ FEATURE IMPORTANCE ANALYSIS\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Prepare data for feature importance\n",
    "# Encode target variable\n",
    "le_target = LabelEncoder()\n",
    "y_encoded = le_target.fit_transform(df_fe['decision_finale'])\n",
    "\n",
    "# Get numerical features for importance analysis\n",
    "numerical_features = df_fe.select_dtypes(include=[np.number]).columns.tolist()\n",
    "X_numerical = df_fe[numerical_features].fillna(0)  # Fill any remaining NaN values\n",
    "\n",
    "print(f\"📊 Analyzing importance of {len(numerical_features)} numerical features\")\n",
    "\n",
    "# Method 1: Random Forest Feature Importance\n",
    "print(\"\\n🌲 Random Forest Feature Importance:\")\n",
    "rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)\n",
    "rf.fit(X_numerical, y_encoded)\n",
    "\n",
    "# Get feature importance\n",
    "feature_importance = pd.DataFrame({\n",
    "    'feature': numerical_features,\n",
    "    'importance': rf.feature_importances_\n",
    "}).sort_values('importance', ascending=False)\n",
    "\n",
    "# Plot top 20 features\n",
    "plt.figure(figsize=(12, 8))\n",
    "top_features = feature_importance.head(20)\n",
    "plt.barh(range(len(top_features)), top_features['importance'], \n",
    "         color=plt.cm.viridis(np.linspace(0, 1, len(top_features))))\n",
    "plt.yticks(range(len(top_features)), top_features['feature'])\n",
    "plt.xlabel('Feature Importance')\n",
    "plt.title('Top 20 Feature Importance (Random Forest)', fontweight='bold')\n",
    "plt.gca().invert_yaxis()\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "print(\"\\n🏆 TOP 15 MOST IMPORTANT FEATURES:\")\n",
    "for i, (_, row) in enumerate(feature_importance.head(15).iterrows(), 1):\n",
    "    print(f\"   {i:2d}. {row['feature']}: {row['importance']:.4f}\")\n",
    "\n",
    "# Method 2: Mutual Information\n",
    "print(\"\\n🔗 Mutual Information Feature Selection:\")\n",
    "mi_scores = mutual_info_classif(X_numerical, y_encoded, random_state=42)\n",
    "mi_importance = pd.DataFrame({\n",
    "    'feature': numerical_features,\n",
    "    'mi_score': mi_scores\n",
    "}).sort_values('mi_score', ascending=False)\n",
    "\n",
    "print(\"\\n🏆 TOP 10 FEATURES BY MUTUAL INFORMATION:\")\n",
    "for i, (_, row) in enumerate(mi_importance.head(10).iterrows(), 1):\n",
    "    print(f\"   {i:2d}. {row['feature']}: {row['mi_score']:.4f}\")\n",
    "\n",
    "# Select top features\n",
    "top_features_rf = feature_importance.head(20)['feature'].tolist()\n",
    "top_features_mi = mi_importance.head(20)['feature'].tolist()\n",
    "\n",
    "# Combine and get unique features\n",
    "selected_features = list(set(top_features_rf + top_features_mi))\n",
    "print(f\"\\n✅ Selected {len(selected_features)} important features for modeling\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "preprocessing",
   "metadata": {},
   "source": [
    "## 8. Data Preprocessing for Modeling"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "preprocessing-pipeline",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final preprocessing for machine learning\n",
    "print(\"🚀 DATA PREPROCESSING FOR MODELING\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Create final dataset\n",
    "df_final = df_fe.copy()\n",
    "\n",
    "# 1. Handle categorical variables\n",
    "print(\"\\n🏷️ Encoding Categorical Variables:\")\n",
    "\n",
    "categorical_features = df_final.select_dtypes(include=['object', 'category']).columns.tolist()\n",
    "categorical_features = [col for col in categorical_features if col != 'decision_finale']  # Exclude target\n",
    "\n",
    "# Label encoding for ordinal variables\n",
    "ordinal_features = ['niveau_education', 'classe_risque']  # These have natural order\n",
    "ordinal_features = [col for col in ordinal_features if col in categorical_features]\n",
    "\n",
    "label_encoders = {}\n",
    "for col in ordinal_features:\n",
    "    le = LabelEncoder()\n",
    "    df_final[f'{col}_encoded'] = le.fit_transform(df_final[col].astype(str))\n",
    "    label_encoders[col] = le\n",
    "    print(f\"   ✅ Label encoded: {col}\")\n",
    "\n",
    "# One-hot encoding for nominal variables\n",
    "nominal_features = [col for col in categorical_features if col not in ordinal_features]\n",
    "nominal_features = [col for col in nominal_features if df_final[col].nunique() <= 10]  # Limit to avoid too many features\n",
    "\n",
    "for col in nominal_features:\n",
    "    # Create dummy variables\n",
    "    dummies = pd.get_dummies(df_final[col], prefix=col, drop_first=True)\n",
    "    df_final = pd.concat([df_final, dummies], axis=1)\n",
    "    print(f\"   ✅ One-hot encoded: {col} ({len(dummies.columns)} new features)\")\n",
    "\n",
    "# 2. Scale numerical features\n",
    "print(\"\\n📏 Scaling Numerical Features:\")\n",
    "\n",
    "# Get all numerical columns (including new engineered features)\n",
    "numerical_features_final = df_final.select_dtypes(include=[np.number]).columns.tolist()\n",
    "\n",
    "# Exclude ID columns and target-related columns\n",
    "exclude_from_scaling = ['cin', 'client_id']  # ID columns shouldn't be scaled\n",
    "numerical_features_final = [col for col in numerical_features_final if col not in exclude_from_scaling]\n",
    "\n",
    "# Apply StandardScaler\n",
    "scaler = StandardScaler()\n",
    "df_final[numerical_features_final] = scaler.fit_transform(df_final[numerical_features_final].fillna(0))\n",
    "\n",
    "print(f\"   ✅ Scaled {len(numerical_features_final)} numerical features\")\n",
    "\n",
    "# 3. Prepare final feature matrix\n",
    "print(\"\\n🎯 Preparing Final Dataset:\")\n",
    "\n",
    "# Define features to use for modeling\n",
    "feature_columns = []\n",
    "\n",
    "# Add selected important numerical features\n",
    "feature_columns.extend([col for col in selected_features if col in df_final.columns])\n",
    "\n",
    "# Add encoded categorical features\n",
    "encoded_categorical = [col for col in df_final.columns if col.endswith('_encoded')]\n",
    "feature_columns.extend(encoded_categorical)\n",
    "\n",
    "# Add one-hot encoded features\n",
    "onehot_features = [col for col in df_final.columns if any(col.startswith(f'{nom}_') for nom in nominal_features)]\n",
    "feature_columns.extend(onehot_features)\n",
    "\n",
    "# Remove duplicates and ensure all features exist\n",
    "feature_columns = list(set(feature_columns))\n",
    "feature_columns = [col for col in feature_columns if col in df_final.columns]\n",
    "\n",
    "# Create final feature matrix\n",
    "X_final = df_final[feature_columns]\n",
    "y_final = df_final['decision_finale']\n",
    "\n",
    "print(f\"   ✅ Final feature matrix: {X_final.shape}\")\n",
    "print(f\"   ✅ Target variable: {y_final.shape}\")\n",
    "print(f\"   ✅ Features selected: {len(feature_columns)}\")\n",
    "\n",
    "# 4. Train-test split\n",
    "print(\"\\n🔄 Creating Train-Test Split:\")\n",
    "\n",
    "X_train, X_test, y_train, y_test = train_test_split(\n",
    "    X_final, y_final, test_size=0.2, random_state=42, stratify=y_final\n",
    ")\n",
    "\n",
    "print(f\"   ✅ Training set: {X_train.shape}\")\n",
    "print(f\"   ✅ Test set: {X_test.shape}\")\n",
    "print(f\"   ✅ Target distribution preserved in split\")\n",
    "\n",
    "# 5. Save preprocessed data\n",
    "print(\"\\n💾 Saving Preprocessed Data:\")\n",
    "\n",
    "# Save the complete preprocessed dataset\n",
    "df_final.to_pickle('tunisian_credit_preprocessed.pkl')\n",
    "print(\"   ✅ Complete preprocessed dataset saved\")\n",
    "\n",
    "# Save train-test splits\n",
    "pd.concat([X_train, y_train], axis=1).to_pickle('train_data.pkl')\n",
    "pd.concat([X_test, y_test], axis=1).to_pickle('test_data.pkl')\n",
    "print(\"   ✅ Train-test splits saved\")\n",
    "\n",
    "# Save feature names and preprocessing objects\n",
    "import pickle\n",
    "with open('preprocessing_objects.pkl', 'wb') as f:\n",
    "    pickle.dump({\n",
    "        'feature_columns': feature_columns,\n",
    "        'scaler': scaler,\n",
    "        'label_encoders': label_encoders,\n",
    "        'target_encoder': le_target\n",
    "    }, f)\n",
    "print(\"   ✅ Preprocessing objects saved\")\n",
    "\n",
    "print(\"\\n🎉 EDA AND FEATURE ENGINEERING COMPLETED!\")\n",
    "print(\"\\n📋 SUMMARY:\")\n",
    "print(f\"   • Original dataset: {df.shape}\")\n",
    "print(f\"   • Final dataset: {df_final.shape}\")\n",
    "print(f\"   • Features for modeling: {len(feature_columns)}\")\n",
    "print(f\"   • Training samples: {len(X_train)}\")\n",
    "print(f\"   • Test samples: {len(X_test)}\")\n",
    "print(\"\\n🚀 Ready for machine learning model development!\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.7"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
