"""
Quick fix for target encoding issue in model comparison
Run this script to fix the target variable encoding problem
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
import pickle

def fix_target_encoding():
    print("🔧 FIXING TARGET ENCODING ISSUE")
    print("=" * 50)
    
    try:
        # Load the cleaned dataset
        print("📂 Loading cleaned dataset...")
        df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
        print(f"✅ Loaded dataset with shape: {df.shape}")
        
        # Check target variable
        print(f"\n🎯 Target variable analysis:")
        print(f"   Column: decision_finale")
        print(f"   Unique values: {df['decision_finale'].unique()}")
        print(f"   Data type: {df['decision_finale'].dtype}")
        
        # Get numerical features
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_columns = [col for col in numerical_cols if col not in ['cin']]
        
        print(f"\n📊 Features selected: {len(feature_columns)}")
        
        # Prepare features and target
        X = df[feature_columns].fillna(0)
        y = df['decision_finale']
        
        # Encode target variable
        print(f"\n🔄 Encoding target variable...")
        target_encoder = LabelEncoder()
        y_encoded = target_encoder.fit_transform(y)
        
        print(f"   Original labels: {target_encoder.classes_}")
        print(f"   Encoded values: {np.unique(y_encoded)}")
        
        # Create train-test split
        print(f"\n✂️ Creating train-test split...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        # Create dataframes with encoded targets
        train_data = pd.concat([
            X_train, 
            pd.Series(y_train, index=X_train.index, name='decision_finale')
        ], axis=1)
        
        test_data = pd.concat([
            X_test, 
            pd.Series(y_test, index=X_test.index, name='decision_finale')
        ], axis=1)
        
        # Save the corrected data
        print(f"\n💾 Saving corrected data...")
        train_data.to_pickle('train_data_fixed.pkl')
        test_data.to_pickle('test_data_fixed.pkl')
        
        # Save preprocessing objects
        preprocessing_objects = {
            'feature_columns': feature_columns,
            'target_encoder': target_encoder,
            'scaler': None  # Will be created in feature engineering if needed
        }
        
        with open('preprocessing_objects_fixed.pkl', 'wb') as f:
            pickle.dump(preprocessing_objects, f)
        
        print(f"✅ Files saved:")
        print(f"   • train_data_fixed.pkl")
        print(f"   • test_data_fixed.pkl") 
        print(f"   • preprocessing_objects_fixed.pkl")
        
        # Show distribution
        print(f"\n📊 Target distribution:")
        for i, class_name in enumerate(target_encoder.classes_):
            count = np.sum(y_encoded == i)
            percentage = (count / len(y_encoded)) * 100
            print(f"   • {class_name}: {count:,} ({percentage:.1f}%)")
        
        print(f"\n✅ Target encoding fixed successfully!")
        print(f"\n📋 Next steps:")
        print(f"   1. Update your model comparison notebook to use:")
        print(f"      - train_data_fixed.pkl")
        print(f"      - test_data_fixed.pkl")
        print(f"      - preprocessing_objects_fixed.pkl")
        print(f"   2. Or run the updated model comparison code")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_target_encoding()
    if success:
        print(f"\n🎉 Fix completed successfully!")
    else:
        print(f"\n💥 Fix failed. Please check the error message above.")
