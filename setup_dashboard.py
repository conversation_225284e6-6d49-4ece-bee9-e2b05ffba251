"""
Setup script for Credit Risk Dashboard
Prepares the best model for use in the Streamlit dashboard
"""

import pandas as pd
import numpy as np
import joblib
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import GradientBoostingClassifier
import os

def setup_dashboard():
    """Prepare model and data for dashboard"""
    print("🔧 SETTING UP CREDIT RISK DASHBOARD")
    print("=" * 50)
    
    try:
        # Load the cleaned dataset
        print("📂 Loading cleaned dataset...")
        df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
        print(f"✅ Dataset loaded: {df.shape}")
        
        # Prepare features
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_columns = [col for col in numerical_cols if col not in ['cin']]
        
        X = df[feature_columns].fillna(0)
        y = df['decision_finale']
        
        # Encode target
        target_encoder = LabelEncoder()
        y_encoded = target_encoder.fit_transform(y)
        
        print(f"📊 Features prepared: {len(feature_columns)}")
        print(f"🎯 Target classes: {target_encoder.classes_}")
        
        # Check if we have a trained model
        model_files = [
            'best_model_gradient_boosting.pkl',
            'best_fast_model_gradient_boosting.pkl',
            'best_model_random_forest.pkl',
            'best_model_xgboost.pkl'
        ]
        
        model = None
        model_name = None
        
        for model_file in model_files:
            if os.path.exists(model_file):
                print(f"📦 Loading model: {model_file}")
                model = joblib.load(model_file)
                model_name = model_file
                break
        
        if model is None:
            print("⚠️ No trained model found. Training a new Gradient Boosting model...")
            
            # Train a new model
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import GradientBoostingClassifier
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
            )
            
            model = GradientBoostingClassifier(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # Save the model
            model_name = 'dashboard_gradient_boosting_model.pkl'
            joblib.dump(model, model_name)
            print(f"✅ New model trained and saved: {model_name}")
        else:
            print(f"✅ Model loaded successfully: {model_name}")
        
        # Test the model
        print("\n🧪 Testing model...")
        sample_prediction = model.predict(X.iloc[:1])
        sample_proba = model.predict_proba(X.iloc[:1])
        print(f"✅ Model test successful")
        
        # Create a standardized model file for dashboard
        dashboard_model_file = 'best_model_gradient_boosting.pkl'
        if model_name != dashboard_model_file:
            joblib.dump(model, dashboard_model_file)
            print(f"✅ Model saved as: {dashboard_model_file}")
        
        # Save feature information
        feature_info = {
            'feature_columns': feature_columns,
            'target_encoder': target_encoder,
            'feature_stats': df[feature_columns].describe()
        }
        
        with open('dashboard_feature_info.pkl', 'wb') as f:
            pickle.dump(feature_info, f)
        print(f"✅ Feature info saved: dashboard_feature_info.pkl")
        
        # Create sample data for testing
        sample_data = df.sample(5)[feature_columns + ['decision_finale']]
        sample_data.to_csv('sample_test_data.csv', index=False)
        print(f"✅ Sample test data saved: sample_test_data.csv")
        
        print(f"\n🎉 DASHBOARD SETUP COMPLETED!")
        print(f"\n📋 Files created:")
        print(f"   • {dashboard_model_file} - Main model file")
        print(f"   • dashboard_feature_info.pkl - Feature information")
        print(f"   • sample_test_data.csv - Sample data for testing")
        
        print(f"\n🚀 Next steps:")
        print(f"   1. Install requirements: pip install -r requirements_dashboard.txt")
        print(f"   2. Run dashboard: streamlit run credit_risk_dashboard.py")
        print(f"   3. Open browser and test with sample data")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during setup: {str(e)}")
        return False

def create_sample_inputs():
    """Create sample input scenarios for testing"""
    print(f"\n📝 CREATING SAMPLE TEST SCENARIOS")
    print("=" * 50)
    
    scenarios = {
        "High Approval Probability": {
            "age": 35,
            "revenu_mensuel": 4000,
            "montant_demande": 30000,
            "duree_demande": 60,
            "dette_totale": 5000,
            "anciennete_emploi": 8,
            "patrimoine_total": 150000,
            "valeur_immobilier": 120000,
            "score_comportement": 0.95,
            "nombre_credits_anterieurs": 2,
            "retard_maximum_jours": 0
        },
        "High Rejection Probability": {
            "age": 25,
            "revenu_mensuel": 800,
            "montant_demande": 100000,
            "duree_demande": 120,
            "dette_totale": 50000,
            "anciennete_emploi": 1,
            "patrimoine_total": 10000,
            "valeur_immobilier": 0,
            "score_comportement": 0.3,
            "nombre_credits_anterieurs": 8,
            "retard_maximum_jours": 90
        },
        "Manual Review Scenario": {
            "age": 45,
            "revenu_mensuel": 2500,
            "montant_demande": 75000,
            "duree_demande": 84,
            "dette_totale": 25000,
            "anciennete_emploi": 5,
            "patrimoine_total": 80000,
            "valeur_immobilier": 60000,
            "score_comportement": 0.7,
            "nombre_credits_anterieurs": 4,
            "retard_maximum_jours": 30
        }
    }
    
    scenarios_df = pd.DataFrame(scenarios).T
    scenarios_df.to_csv('test_scenarios.csv')
    print(f"✅ Test scenarios saved: test_scenarios.csv")
    
    for scenario_name, values in scenarios.items():
        print(f"\n🔹 {scenario_name}:")
        for key, value in values.items():
            print(f"   {key}: {value}")

if __name__ == "__main__":
    success = setup_dashboard()
    if success:
        create_sample_inputs()
        print(f"\n🎉 Setup completed successfully!")
        print(f"\n🚀 Ready to launch dashboard!")
    else:
        print(f"\n💥 Setup failed. Please check the errors above.")
