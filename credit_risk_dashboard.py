"""
Credit Risk Prediction Dashboard
Interactive Streamlit app for real-time credit decision predictions
"""

import streamlit as st
import pandas as pd
import numpy as np
import pickle
import joblib
from sklearn.preprocessing import LabelEncoder, StandardScaler
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Page configuration
st.set_page_config(
    page_title="Credit Risk Prediction Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .prediction-box {
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
    }
    .approved {
        background-color: #d4edda;
        color: #155724;
        border: 2px solid #c3e6cb;
    }
    .rejected {
        background-color: #f8d7da;
        color: #721c24;
        border: 2px solid #f5c6cb;
    }
    .manual-review {
        background-color: #fff3cd;
        color: #856404;
        border: 2px solid #ffeaa7;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f77b4;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_model_and_data():
    """Load the trained model and preprocessing objects"""
    try:
        # Try to load the best model (adjust filename as needed)
        model = joblib.load('best_model_gradient_boosting.pkl')
        
        # Load sample data to get feature names and ranges
        df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
        
        # Get numerical features
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_columns = [col for col in numerical_cols if col not in ['cin']]
        
        # Get feature statistics for input validation
        feature_stats = df[feature_columns].describe()
        
        # Target encoder
        target_encoder = LabelEncoder()
        target_encoder.fit(df['decision_finale'])
        
        return model, feature_columns, feature_stats, target_encoder, df
        
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None, None, None, None, None

def create_input_form(feature_columns, feature_stats):
    """Create input form for user data"""
    st.sidebar.header("📝 Customer Information")
    
    inputs = {}
    
    # Personal Information
    st.sidebar.subheader("👤 Personal Details")
    inputs['age'] = st.sidebar.slider(
        "Age", 
        min_value=18, 
        max_value=80, 
        value=35,
        help="Customer's age in years"
    )
    
    inputs['nombre_enfants'] = st.sidebar.selectbox(
        "Number of Children",
        options=[0, 1, 2, 3, 4, 5],
        index=1,
        help="Number of dependent children"
    )
    
    # Financial Information
    st.sidebar.subheader("💰 Financial Details")
    
    if 'revenu_mensuel' in feature_columns:
        inputs['revenu_mensuel'] = st.sidebar.number_input(
            "Monthly Income (TND)",
            min_value=0.0,
            max_value=20000.0,
            value=2000.0,
            step=100.0,
            help="Monthly income in Tunisian Dinars"
        )
    
    if 'montant_demande' in feature_columns:
        inputs['montant_demande'] = st.sidebar.number_input(
            "Requested Loan Amount (TND)",
            min_value=1000.0,
            max_value=500000.0,
            value=50000.0,
            step=1000.0,
            help="Amount of credit requested"
        )
    
    if 'duree_demande' in feature_columns:
        inputs['duree_demande'] = st.sidebar.selectbox(
            "Loan Duration (months)",
            options=[12, 24, 36, 48, 60, 72, 84, 96, 120],
            index=4,
            help="Requested loan duration in months"
        )
    
    if 'dette_totale' in feature_columns:
        inputs['dette_totale'] = st.sidebar.number_input(
            "Total Existing Debt (TND)",
            min_value=0.0,
            max_value=200000.0,
            value=10000.0,
            step=1000.0,
            help="Total existing debt"
        )
    
    # Employment Information
    st.sidebar.subheader("💼 Employment")
    
    if 'anciennete_emploi' in feature_columns:
        inputs['anciennete_emploi'] = st.sidebar.slider(
            "Employment Tenure (years)",
            min_value=0,
            max_value=40,
            value=5,
            help="Years in current employment"
        )
    
    # Assets
    st.sidebar.subheader("🏠 Assets")
    
    if 'patrimoine_total' in feature_columns:
        inputs['patrimoine_total'] = st.sidebar.number_input(
            "Total Assets (TND)",
            min_value=0.0,
            max_value=1000000.0,
            value=100000.0,
            step=5000.0,
            help="Total value of assets"
        )
    
    if 'valeur_immobilier' in feature_columns:
        inputs['valeur_immobilier'] = st.sidebar.number_input(
            "Real Estate Value (TND)",
            min_value=0.0,
            max_value=800000.0,
            value=80000.0,
            step=5000.0,
            help="Value of real estate owned"
        )
    
    # Credit History
    st.sidebar.subheader("📊 Credit History")
    
    if 'score_comportement' in feature_columns:
        inputs['score_comportement'] = st.sidebar.slider(
            "Behavioral Score",
            min_value=0.0,
            max_value=1.0,
            value=0.8,
            step=0.01,
            help="Credit behavioral score (0-1)"
        )
    
    if 'nombre_credits_anterieurs' in feature_columns:
        inputs['nombre_credits_anterieurs'] = st.sidebar.selectbox(
            "Previous Credits",
            options=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            index=2,
            help="Number of previous credit applications"
        )
    
    if 'retard_maximum_jours' in feature_columns:
        inputs['retard_maximum_jours'] = st.sidebar.selectbox(
            "Maximum Delay (days)",
            options=[0, 15, 30, 60, 90, 120],
            index=0,
            help="Maximum payment delay in previous credits"
        )
    
    # Fill remaining features with median values
    for col in feature_columns:
        if col not in inputs:
            if col in feature_stats.columns:
                inputs[col] = feature_stats[col]['50%']  # median
            else:
                inputs[col] = 0
    
    return inputs

def make_prediction(model, inputs, feature_columns, target_encoder):
    """Make prediction based on user inputs"""
    try:
        # Create input dataframe
        input_df = pd.DataFrame([inputs])
        
        # Ensure all required features are present
        for col in feature_columns:
            if col not in input_df.columns:
                input_df[col] = 0
        
        # Select only the features used in training
        input_df = input_df[feature_columns]
        
        # Make prediction
        prediction = model.predict(input_df)[0]
        prediction_proba = model.predict_proba(input_df)[0]
        
        # Get class names
        class_names = target_encoder.classes_
        predicted_class = class_names[prediction]
        
        # Create probability dictionary
        probabilities = {class_names[i]: prob for i, prob in enumerate(prediction_proba)}
        
        return predicted_class, probabilities
        
    except Exception as e:
        st.error(f"Error making prediction: {str(e)}")
        return None, None

def display_prediction_result(predicted_class, probabilities):
    """Display prediction results with styling"""
    
    # Main prediction
    if predicted_class == 'APPROVE':
        st.markdown(f'<div class="prediction-box approved">✅ APPROVED</div>', unsafe_allow_html=True)
        st.success("🎉 Congratulations! Your credit application is likely to be approved.")
    elif predicted_class == 'REJECT':
        st.markdown(f'<div class="prediction-box rejected">❌ REJECTED</div>', unsafe_allow_html=True)
        st.error("😞 Unfortunately, your credit application is likely to be rejected.")
    else:  # MANUAL_REVIEW
        st.markdown(f'<div class="prediction-box manual-review">⏳ MANUAL REVIEW</div>', unsafe_allow_html=True)
        st.warning("🔍 Your application requires manual review by our credit team.")
    
    # Probability breakdown
    st.subheader("📊 Prediction Confidence")
    
    # Create probability chart
    prob_df = pd.DataFrame(list(probabilities.items()), columns=['Decision', 'Probability'])
    prob_df['Probability_Percent'] = prob_df['Probability'] * 100
    
    fig = px.bar(
        prob_df, 
        x='Decision', 
        y='Probability_Percent',
        title='Prediction Probabilities',
        color='Decision',
        color_discrete_map={
            'APPROVE': '#28a745',
            'REJECT': '#dc3545', 
            'MANUAL_REVIEW': '#ffc107'
        }
    )
    fig.update_layout(showlegend=False, yaxis_title="Probability (%)")
    st.plotly_chart(fig, use_container_width=True)
    
    # Detailed probabilities
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "✅ Approval Probability", 
            f"{probabilities.get('APPROVE', 0):.1%}",
            delta=None
        )
    
    with col2:
        st.metric(
            "❌ Rejection Probability", 
            f"{probabilities.get('REJECT', 0):.1%}",
            delta=None
        )
    
    with col3:
        st.metric(
            "⏳ Manual Review Probability", 
            f"{probabilities.get('MANUAL_REVIEW', 0):.1%}",
            delta=None
        )

def main():
    """Main dashboard function"""
    
    # Header
    st.markdown('<h1 class="main-header">🏦 Credit Risk Prediction Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load model and data
    with st.spinner("Loading model and data..."):
        model, feature_columns, feature_stats, target_encoder, df = load_model_and_data()
    
    if model is None:
        st.error("❌ Failed to load model. Please ensure the model file exists.")
        st.info("💡 Make sure you have run the model training script first.")
        return
    
    st.success("✅ Model loaded successfully!")
    
    # Sidebar inputs
    inputs = create_input_form(feature_columns, feature_stats)
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🎯 Credit Decision Prediction")
        
        # Prediction button
        if st.button("🔮 Predict Credit Decision", type="primary", use_container_width=True):
            with st.spinner("Analyzing your application..."):
                predicted_class, probabilities = make_prediction(
                    model, inputs, feature_columns, target_encoder
                )
                
                if predicted_class and probabilities:
                    display_prediction_result(predicted_class, probabilities)
    
    with col2:
        st.header("📋 Application Summary")
        
        # Display key inputs
        st.markdown("### Key Information")
        st.write(f"**Age:** {inputs.get('age', 'N/A')} years")
        st.write(f"**Monthly Income:** {inputs.get('revenu_mensuel', 0):,.0f} TND")
        st.write(f"**Requested Amount:** {inputs.get('montant_demande', 0):,.0f} TND")
        st.write(f"**Loan Duration:** {inputs.get('duree_demande', 'N/A')} months")
        st.write(f"**Total Debt:** {inputs.get('dette_totale', 0):,.0f} TND")
        
        # Calculate some ratios
        if inputs.get('revenu_mensuel', 0) > 0:
            debt_to_income = inputs.get('dette_totale', 0) / (inputs.get('revenu_mensuel', 1) * 12)
            loan_to_income = inputs.get('montant_demande', 0) / (inputs.get('revenu_mensuel', 1) * 12)
            
            st.markdown("### Financial Ratios")
            st.write(f"**Debt-to-Income:** {debt_to_income:.1%}")
            st.write(f"**Loan-to-Income:** {loan_to_income:.1%}")
    
    # Footer
    st.markdown("---")
    st.markdown("### ℹ️ About This Dashboard")
    st.info("""
    This dashboard uses a machine learning model trained on historical credit data to predict credit decisions.
    The model achieved 98.8% accuracy on test data. However, this is for demonstration purposes only and 
    should not be used for actual credit decisions without proper validation and regulatory approval.
    """)

if __name__ == "__main__":
    main()
