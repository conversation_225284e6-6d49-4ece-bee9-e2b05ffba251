{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "title",
   "metadata": {},
   "source": [
    "# Tunisian Credit Dataset - Information & Cleaning Guide\n",
    "\n",
    "This notebook provides a comprehensive step-by-step guide for analyzing and cleaning the Tunisian credit dataset.\n",
    "\n",
    "## Dataset Overview\n",
    "- **Size**: 100,000 records\n",
    "- **Purpose**: Credit risk assessment for Tunisian banking\n",
    "- **Target Variable**: `decision_finale` (Final credit decision)\n",
    "- **Features**: 55 columns covering personal, financial, and credit history information"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import necessary libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from scipy import stats\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set display options\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.width', None)\n",
    "pd.set_option('display.max_colwidth', 50)\n",
    "\n",
    "print(\"Libraries imported successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step1",
   "metadata": {},
   "source": [
    "## Step 1: Fix CSV Parsing Issue\n",
    "\n",
    "**Problem Identified**: The dataset uses semicolons (`;`) as delimiters, not commas.\n",
    "**Solution**: Use `sep=';'` parameter in `pd.read_csv()`"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "load-data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the dataset with correct delimiter\n",
    "df = pd.read_csv('tunisian_credit_dataset_100k.csv', sep=';')\n",
    "\n",
    "print(f\"Dataset shape: {df.shape}\")\n",
    "print(f\"Number of columns: {len(df.columns)}\")\n",
    "print(f\"Number of rows: {len(df)}\")\n",
    "print(\"\\n✅ CSV parsing issue fixed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step2",
   "metadata": {},
   "source": [
    "## Step 2: Dataset Information & Column Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "column-info",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display all column names with numbering\n",
    "print(\"📋 COLUMN NAMES:\")\n",
    "print(\"=\" * 50)\n",
    "for i, col in enumerate(df.columns, 1):\n",
    "    print(f\"{i:2d}. {col}\")\n",
    "\n",
    "print(f\"\\n📊 BASIC DATASET INFO:\")\n",
    "print(\"=\" * 50)\n",
    "df.info(memory_usage='deep')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "sample-data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display first few rows\n",
    "print(\"🔍 FIRST 3 ROWS:\")\n",
    "print(\"=\" * 50)\n",
    "display(df.head(3))\n",
    "\n",
    "print(\"\\n🔍 LAST 3 ROWS:\")\n",
    "print(\"=\" * 50)\n",
    "display(df.tail(3))"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "data-dictionary",
   "metadata": {},
   "source": [
    "## Step 3: Data Dictionary & Column Descriptions\n",
    "\n",
    "### 👤 Personal Information\n",
    "- **client_id**: Unique client identifier (UUID)\n",
    "- **cin**: National ID number\n",
    "- **age**: Client age in years\n",
    "- **sexe**: Gender (M/F)\n",
    "- **situation_familiale**: Marital status\n",
    "- **nombre_enfants**: Number of children\n",
    "- **niveau_education**: Education level\n",
    "- **region**: Geographic region in Tunisia\n",
    "- **profession**: Job title/profession\n",
    "- **secteur_activite**: Industry sector\n",
    "\n",
    "### 💼 Employment & Income\n",
    "- **anciennete_emploi**: Employment tenure (years)\n",
    "- **type_contrat**: Contract type (CDI, CDD, etc.)\n",
    "- **revenu_mensuel**: Monthly income (TND)\n",
    "- **autres_revenus**: Other income sources (TND)\n",
    "- **revenu_total**: Total monthly income (TND)\n",
    "\n",
    "### 🏠 Housing & Assets\n",
    "- **type_logement**: Housing type (owner, tenant, etc.)\n",
    "- **anciennete_logement**: Housing tenure (years)\n",
    "- **valeur_immobilier**: Real estate value (TND)\n",
    "- **valeur_vehicule**: Vehicle value (TND)\n",
    "- **epargne**: Savings amount (TND)\n",
    "- **patrimoine_total**: Total assets (TND)\n",
    "\n",
    "### 💳 Debt & Financial Ratios\n",
    "- **dette_immobiliere**: Real estate debt (TND)\n",
    "- **dette_auto**: Auto loan debt (TND)\n",
    "- **dette_personnelle**: Personal debt (TND)\n",
    "- **dette_totale**: Total debt (TND)\n",
    "- **ratio_endettement**: Debt-to-income ratio\n",
    "- **reste_a_vivre**: Disposable income (TND)\n",
    "- **capacite_remboursement**: Repayment capacity (TND)\n",
    "\n",
    "### 🏦 Credit History & Banking\n",
    "- **nombre_credits_anterieurs**: Previous credit count\n",
    "- **anciennete_relation_bancaire**: Banking relationship duration (years)\n",
    "- **banque_principale**: Main bank name\n",
    "- **retard_maximum_jours**: Maximum payment delay (days)\n",
    "- **nombre_incidents_12m**: Payment incidents in last 12 months\n",
    "- **nombre_demandes_6m**: Credit applications in last 6 months\n",
    "- **taux_utilisation_credit**: Credit utilization rate\n",
    "- **regularite_paiements**: Payment regularity score\n",
    "- **nombre_rejets_12m**: Credit rejections in last 12 months\n",
    "- **score_comportement**: Behavioral credit score\n",
    "\n",
    "### 📋 Current Application\n",
    "- **montant_demande**: Requested loan amount (TND)\n",
    "- **duree_demande**: Requested loan duration (months)\n",
    "- **type_credit**: Credit type (personal, auto, mortgage, etc.)\n",
    "- **mensualite_demandee**: Requested monthly payment (TND)\n",
    "- **taux_propose**: Proposed interest rate\n",
    "- **ratio_mensualite_revenu**: Payment-to-income ratio\n",
    "- **apport_personnel**: Personal contribution/down payment (TND)\n",
    "- **valeur_garanties**: Collateral value (TND)\n",
    "\n",
    "### ⚠️ Risk Assessment\n",
    "- **classe_risque**: Risk class (C0, C1, C2, etc.)\n",
    "- **situation_contentieux**: Litigation status (VRAI/FAUX)\n",
    "- **default_flag**: Default indicator (VRAI/FAUX)\n",
    "- **score_pd**: Probability of Default score\n",
    "- **score_lgd**: Loss Given Default score\n",
    "- **score_ead**: Exposure at Default score\n",
    "- **perte_attendue**: Expected loss (TND)\n",
    "- **decision_finale**: Final credit decision (TARGET VARIABLE)\n",
    "\n",
    "### 🌍 External Factors\n",
    "- **taux_chomage_sectoriel**: Sectoral unemployment rate\n",
    "- **garanties_disponibles**: Available guarantees (TND)"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step3",
   "metadata": {},
   "source": [
    "## Step 4: Data Quality Assessment"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "missing-values",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check for missing values\n",
    "print(\"🔍 MISSING VALUES ANALYSIS:\")\n",
    "print(\"=\" * 50)\n",
    "missing_data = df.isnull().sum()\n",
    "missing_percent = (missing_data / len(df)) * 100\n",
    "\n",
    "missing_df = pd.DataFrame({\n",
    "    'Column': missing_data.index,\n",
    "    'Missing_Count': missing_data.values,\n",
    "    'Missing_Percentage': missing_percent.values\n",
    "})\n",
    "\n",
    "# Show only columns with missing values\n",
    "missing_df_filtered = missing_df[missing_df['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)\n",
    "\n",
    "if len(missing_df_filtered) > 0:\n",
    "    print(f\"Found {len(missing_df_filtered)} columns with missing values:\")\n",
    "    display(missing_df_filtered)\n",
    "else:\n",
    "    print(\"✅ No missing values found in the dataset!\")\n",
    "\n",
    "print(f\"\\nTotal missing values: {missing_data.sum()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "duplicates",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check for duplicate rows\n",
    "print(\"🔍 DUPLICATE ANALYSIS:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Check for complete duplicates\n",
    "total_duplicates = df.duplicated().sum()\n",
    "print(f\"Complete duplicate rows: {total_duplicates}\")\n",
    "\n",
    "# Check for duplicate client_ids\n",
    "duplicate_clients = df['client_id'].duplicated().sum()\n",
    "print(f\"Duplicate client_ids: {duplicate_clients}\")\n",
    "\n",
    "# Check for duplicate CINs (National IDs)\n",
    "duplicate_cins = df['cin'].duplicated().sum()\n",
    "print(f\"Duplicate CINs: {duplicate_cins}\")\n",
    "\n",
    "if total_duplicates == 0 and duplicate_clients == 0 and duplicate_cins == 0:\n",
    "    print(\"\\n✅ No duplicates found!\")\n",
    "else:\n",
    "    print(\"\\n⚠️ Duplicates detected - will need cleaning\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "data-types",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyze data types and suggest improvements\n",
    "print(\"🔍 DATA TYPES ANALYSIS:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "dtype_info = pd.DataFrame({\n",
    "    'Column': df.columns,\n",
    "    'Data_Type': df.dtypes,\n",
    "    'Non_Null_Count': df.count(),\n",
    "    'Unique_Values': [df[col].nunique() for col in df.columns],\n",
    "    'Memory_Usage_MB': [df[col].memory_usage(deep=True) / 1024**2 for col in df.columns]\n",
    "})\n",
    "\n",
    "print(\"Current data types:\")\n",
    "display(dtype_info)\n",
    "\n",
    "print(f\"\\nTotal memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step4",
   "metadata": {},
   "source": [
    "## Step 5: Categorical Variables Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "categorical-analysis",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Identify and analyze categorical columns\n",
    "print(\"🔍 CATEGORICAL VARIABLES ANALYSIS:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Identify categorical columns (object type or low unique values)\n",
    "categorical_cols = []\n",
    "for col in df.columns:\n",
    "    if df[col].dtype == 'object' or df[col].nunique() < 20:\n",
    "        categorical_cols.append(col)\n",
    "\n",
    "print(f\"Identified {len(categorical_cols)} categorical columns:\")\n",
    "for col in categorical_cols:\n",
    "    unique_count = df[col].nunique()\n",
    "    print(f\"  • {col}: {unique_count} unique values\")\n",
    "\n",
    "# Show value counts for key categorical variables\n",
    "key_categorical = ['sexe', 'situation_familiale', 'niveau_education', 'type_contrat', \n",
    "                  'type_logement', 'banque_principale', 'type_credit', 'classe_risque', \n",
    "                  'situation_contentieux', 'default_flag', 'decision_finale']\n",
    "\n",
    "for col in key_categorical:\n",
    "    if col in df.columns:\n",
    "        print(f\"\\n📊 {col.upper()}:\")\n",
    "        print(\"-\" * 30)\n",
    "        value_counts = df[col].value_counts()\n",
    "        percentages = (value_counts / len(df) * 100).round(2)\n",
    "        result_df = pd.DataFrame({\n",
    "            'Count': value_counts,\n",
    "            'Percentage': percentages\n",
    "        })\n",
    "        display(result_df.head(10))  # Show top 10 values"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "step5",
   "metadata": {},
   "source": [
    "## Step 6: Numerical Variables Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "numerical-analysis",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Identify numerical columns\n",
    "print(\"🔍 NUMERICAL VARIABLES ANALYSIS:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n",
    "print(f\"Identified {len(numerical_cols)} numerical columns:\")\n",
    "for col in numerical_cols:\n",
    "    print(f\"  • {col}\")\n",
    "\n",
    "# Basic statistics for numerical columns\n",
    "if len(numerical_cols) > 0:\n",
    "    print(\"\\n📊 DESCRIPTIVE STATISTICS:\")\n",
    "    print(\"=\" * 50)\n",
    "    display(df[numerical_cols].describe())\n",
    "else:\n",
    "    print(\"\\n⚠️ No numerical columns detected yet - need data type conversion\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "outlier-detection",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Outlier detection for numerical columns\n",
    "def detect_outliers_iqr(df, column):\n",
    "    \"\"\"Detect outliers using IQR method\"\"\"\n",
    "    Q1 = df[column].quantile(0.25)\n",
    "    Q3 = df[column].quantile(0.75)\n",
    "    IQR = Q3 - Q1\n",
    "    lower_bound = Q1 - 1.5 * IQR\n",
    "    upper_bound = Q3 + 1.5 * IQR\n",
    "    outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]\n",
    "    return len(outliers), lower_bound, upper_bound\n",
    "\n",
    "print(\"🔍 OUTLIER DETECTION (IQR Method):\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "if len(numerical_cols) > 0:\n",
    "    outlier_summary = []\n",
    "    for col in numerical_cols:\n",
    "        outlier_count, lower, upper = detect_outliers_iqr(df, col)\n",
    "        outlier_percentage = (outlier_count / len(df)) * 100\n",
    "        outlier_summary.append({\n",
    "            'Column': col,\n",
    "            'Outlier_Count': outlier_count,\n",
    "            'Outlier_Percentage': round(outlier_percentage, 2),\n",
    "            'Lower_Bound': round(lower, 2),\n",
    "            'Upper_Bound': round(upper, 2)\n",
    "        })\n",
    "    \n",
    "    outlier_df = pd.DataFrame(outlier_summary)\n",
    "    outlier_df = outlier_df.sort_values('Outlier_Percentage', ascending=False)\n",
    "    display(outlier_df)\n",
    "else:\n",
    "    print(\"No numerical columns available for outlier detection\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step6",
   "metadata": {},
   "source": [
    "## Step 7: Data Type Optimization & Conversion"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "data-type-conversion",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a copy for cleaning\n",
    "df_clean = df.copy()\n",
    "\n",
    "print(\"🔧 DATA TYPE OPTIMIZATION:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Define columns that should be numeric\n",
    "numeric_columns = [\n",
    "    'age', 'nombre_enfants', 'anciennete_emploi', 'anciennete_logement',\n",
    "    'taux_chomage_sectoriel', 'revenu_mensuel', 'autres_revenus', 'revenu_total',\n",
    "    'valeur_immobilier', 'valeur_vehicule', 'epargne', 'patrimoine_total',\n",
    "    'dette_immobiliere', 'dette_auto', 'dette_personnelle', 'dette_totale',\n",
    "    'ratio_endettement', 'reste_a_vivre', 'capacite_remboursement',\n",
    "    'garanties_disponibles', 'nombre_credits_anterieurs', 'anciennete_relation_bancaire',\n",
    "    'retard_maximum_jours', 'nombre_incidents_12m', 'nombre_demandes_6m',\n",
    "    'taux_utilisation_credit', 'regularite_paiements', 'nombre_rejets_12m',\n",
    "    'score_comportement', 'montant_demande', 'duree_demande', 'mensualite_demandee',\n",
    "    'taux_propose', 'ratio_mensualite_revenu', 'apport_personnel', 'valeur_garanties',\n",
    "    'score_pd', 'score_lgd', 'score_ead', 'perte_attendue'\n",
    "]\n",
    "\n",
    "# Convert to numeric\n",
    "conversion_results = []\n",
    "for col in numeric_columns:\n",
    "    if col in df_clean.columns:\n",
    "        try:\n",
    "            original_type = df_clean[col].dtype\n",
    "            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')\n",
    "            new_type = df_clean[col].dtype\n",
    "            conversion_results.append({\n",
    "                'Column': col,\n",
    "                'Original_Type': original_type,\n",
    "                'New_Type': new_type,\n",
    "                'Status': '✅ Success'\n",
    "            })\n",
    "        except Exception as e:\n",
    "            conversion_results.append({\n",
    "                'Column': col,\n",
    "                'Original_Type': df_clean[col].dtype,\n",
    "                'New_Type': 'Failed',\n",
    "                'Status': f'❌ Error: {str(e)}'\n",
    "            })\n",
    "\n",
    "conversion_df = pd.DataFrame(conversion_results)\n",
    "display(conversion_df)\n",
    "\n",
    "print(f\"\\n✅ Converted {len([r for r in conversion_results if 'Success' in r['Status']])} columns to numeric\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "categorical-optimization",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Optimize categorical columns\n",
    "print(\"\\n🔧 CATEGORICAL OPTIMIZATION:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "categorical_columns = [\n",
    "    'sexe', 'situation_familiale', 'niveau_education', 'region', 'profession',\n",
    "    'secteur_activite', 'type_contrat', 'type_logement', 'banque_principale',\n",
    "    'type_credit', 'classe_risque', 'situation_contentieux', 'default_flag',\n",
    "    'decision_finale'\n",
    "]\n",
    "\n",
    "memory_before = df_clean.memory_usage(deep=True).sum() / 1024**2\n",
    "\n",
    "for col in categorical_columns:\n",
    "    if col in df_clean.columns:\n",
    "        # Convert to category for memory efficiency\n",
    "        df_clean[col] = df_clean[col].astype('category')\n",
    "\n",
    "memory_after = df_clean.memory_usage(deep=True).sum() / 1024**2\n",
    "memory_saved = memory_before - memory_after\n",
    "\n",
    "print(f\"Memory usage before: {memory_before:.2f} MB\")\n",
    "print(f\"Memory usage after: {memory_after:.2f} MB\")\n",
    "print(f\"Memory saved: {memory_saved:.2f} MB ({(memory_saved/memory_before)*100:.1f}%)\")"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "step7",
   "metadata": {},
   "source": [
    "## Step 8: Data Cleaning Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "handle-missing-values",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Handle missing values after type conversion\n",
    "print(\"🔧 HANDLING MISSING VALUES:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Check missing values after conversion\n",
    "missing_after_conversion = df_clean.isnull().sum()\n",
    "missing_cols = missing_after_conversion[missing_after_conversion > 0]\n",
    "\n",
    "if len(missing_cols) > 0:\n",
    "    print(\"Missing values found after type conversion:\")\n",
    "    for col, count in missing_cols.items():\n",
    "        percentage = (count / len(df_clean)) * 100\n",
    "        print(f\"  • {col}: {count} ({percentage:.2f}%)\")\n",
    "    \n",
    "    # Strategy for handling missing values\n",
    "    print(\"\\nApplying missing value strategies:\")\n",
    "    \n",
    "    # For numerical columns: fill with median\n",
    "    numerical_missing = [col for col in missing_cols.index if df_clean[col].dtype in ['float64', 'int64']]\n",
    "    for col in numerical_missing:\n",
    "        median_val = df_clean[col].median()\n",
    "        df_clean[col].fillna(median_val, inplace=True)\n",
    "        print(f\"  ✅ {col}: filled with median ({median_val})\")\n",
    "    \n",
    "    # For categorical columns: fill with mode\n",
    "    categorical_missing = [col for col in missing_cols.index if df_clean[col].dtype == 'category']\n",
    "    for col in categorical_missing:\n",
    "        mode_val = df_clean[col].mode()[0] if len(df_clean[col].mode()) > 0 else 'Unknown'\n",
    "        df_clean[col].fillna(mode_val, inplace=True)\n",
    "        print(f\"  ✅ {col}: filled with mode ({mode_val})\")\n",
    "        \n",
    "else:\n",
    "    print(\"✅ No missing values to handle!\")\n",
    "\n",
    "print(f\"\\nFinal missing values: {df_clean.isnull().sum().sum()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "handle-duplicates",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Handle duplicates\n",
    "print(\"🔧 HANDLING DUPLICATES:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "initial_rows = len(df_clean)\n",
    "\n",
    "# Remove complete duplicates\n",
    "df_clean.drop_duplicates(inplace=True)\n",
    "after_complete_dup = len(df_clean)\n",
    "complete_dup_removed = initial_rows - after_complete_dup\n",
    "\n",
    "# Check for duplicate client_ids and keep the first occurrence\n",
    "df_clean.drop_duplicates(subset=['client_id'], keep='first', inplace=True)\n",
    "after_client_dup = len(df_clean)\n",
    "client_dup_removed = after_complete_dup - after_client_dup\n",
    "\n",
    "print(f\"Complete duplicates removed: {complete_dup_removed}\")\n",
    "print(f\"Client ID duplicates removed: {client_dup_removed}\")\n",
    "print(f\"Final dataset size: {len(df_clean)} rows\")\n",
    "\n",
    "if complete_dup_removed + client_dup_removed == 0:\n",
    "    print(\"✅ No duplicates found!\")\n",
    "else:\n",
    "    print(f\"✅ Removed {complete_dup_removed + client_dup_removed} duplicate rows\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "outlier-treatment",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Outlier treatment for key financial variables\n",
    "print(\"🔧 OUTLIER TREATMENT:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Define key financial columns for outlier treatment\n",
    "financial_cols = ['revenu_mensuel', 'revenu_total', 'montant_demande', 'dette_totale', \n",
    "                 'patrimoine_total', 'valeur_immobilier', 'valeur_vehicule']\n",
    "\n",
    "outlier_treatment_summary = []\n",
    "\n",
    "for col in financial_cols:\n",
    "    if col in df_clean.columns and df_clean[col].dtype in ['float64', 'int64']:\n",
    "        # Calculate IQR bounds\n",
    "        Q1 = df_clean[col].quantile(0.25)\n",
    "        Q3 = df_clean[col].quantile(0.75)\n",
    "        IQR = Q3 - Q1\n",
    "        lower_bound = Q1 - 1.5 * IQR\n",
    "        upper_bound = Q3 + 1.5 * IQR\n",
    "        \n",
    "        # Count outliers before treatment\n",
    "        outliers_before = len(df_clean[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)])\n",
    "        \n",
    "        # Cap outliers (Winsorization)\n",
    "        df_clean[col] = np.where(df_clean[col] < lower_bound, lower_bound, df_clean[col])\n",
    "        df_clean[col] = np.where(df_clean[col] > upper_bound, upper_bound, df_clean[col])\n",
    "        \n",
    "        # Count outliers after treatment\n",
    "        outliers_after = len(df_clean[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)])\n",
    "        \n",
    "        outlier_treatment_summary.append({\n",
    "            'Column': col,\n",
    "            'Outliers_Before': outliers_before,\n",
    "            'Outliers_After': outliers_after,\n",
    "            'Outliers_Treated': outliers_before - outliers_after,\n",
    "            'Lower_Bound': round(lower_bound, 2),\n",
    "            'Upper_Bound': round(upper_bound, 2)\n",
    "        })\n",
    "\n",
    "if outlier_treatment_summary:\n",
    "    outlier_df = pd.DataFrame(outlier_treatment_summary)\n",
    "    display(outlier_df)\n",
    "    total_treated = outlier_df['Outliers_Treated'].sum()\n",
    "    print(f\"\\n✅ Treated {total_treated} outliers using Winsorization\")\n",
    "else:\n",
    "    print(\"No financial columns available for outlier treatment\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step8",
   "metadata": {},
   "source": [
    "## Step 9: Final Data Validation & Summary"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "final-validation",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final data quality report\n",
    "print(\"📊 FINAL DATA QUALITY REPORT:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "print(f\"Dataset Shape: {df_clean.shape}\")\n",
    "print(f\"Memory Usage: {df_clean.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n",
    "print(f\"Missing Values: {df_clean.isnull().sum().sum()}\")\n",
    "print(f\"Duplicate Rows: {df_clean.duplicated().sum()}\")\n",
    "\n",
    "print(\"\\n📈 DATA TYPES SUMMARY:\")\n",
    "print(\"-\" * 30)\n",
    "dtype_summary = df_clean.dtypes.value_counts()\n",
    "for dtype, count in dtype_summary.items():\n",
    "    print(f\"{dtype}: {count} columns\")\n",
    "\n",
    "print(\"\\n🎯 TARGET VARIABLE DISTRIBUTION:\")\n",
    "print(\"-\" * 30)\n",
    "if 'decision_finale' in df_clean.columns:\n",
    "    target_dist = df_clean['decision_finale'].value_counts()\n",
    "    target_pct = (target_dist / len(df_clean) * 100).round(2)\n",
    "    target_summary = pd.DataFrame({\n",
    "        'Count': target_dist,\n",
    "        'Percentage': target_pct\n",
    "    })\n",
    "    display(target_summary)\n",
    "else:\n",
    "    print(\"Target variable 'decision_finale' not found\")\n",
    "\n",
    "print(\"\\n✅ DATA CLEANING COMPLETED SUCCESSFULLY!\")\n",
    "print(\"\\n📋 NEXT STEPS:\")\n",
    "print(\"1. Exploratory Data Analysis (EDA)\")\n",
    "print(\"2. Feature Engineering\")\n",
    "print(\"3. Model Development\")\n",
    "print(\"4. Model Evaluation\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "save-cleaned-data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save the cleaned dataset\n",
    "print(\"💾 SAVING CLEANED DATASET:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Save to CSV\n",
    "df_clean.to_csv('tunisian_credit_dataset_cleaned.csv', index=False)\n",
    "print(\"✅ Cleaned dataset saved as 'tunisian_credit_dataset_cleaned.csv'\")\n",
    "\n",
    "# Save to pickle for faster loading\n",
    "df_clean.to_pickle('tunisian_credit_dataset_cleaned.pkl')\n",
    "print(\"✅ Cleaned dataset saved as 'tunisian_credit_dataset_cleaned.pkl'\")\n",
    "\n",
    "print(f\"\\nFinal dataset ready for analysis with {len(df_clean)} rows and {len(df_clean.columns)} columns!\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.7"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
