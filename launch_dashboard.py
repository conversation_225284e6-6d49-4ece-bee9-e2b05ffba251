"""
Quick launcher for Credit Risk Dashboard
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_dashboard.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        return False

def setup_dashboard():
    """Run dashboard setup"""
    print("🔧 Setting up dashboard...")
    try:
        subprocess.check_call([sys.executable, "setup_dashboard.py"])
        print("✅ Dashboard setup completed!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Dashboard setup failed")
        return False

def launch_streamlit():
    """Launch Streamlit dashboard"""
    print("🚀 Launching Streamlit dashboard...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "credit_risk_dashboard.py"])
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error launching dashboard: {str(e)}")

def main():
    print("🏦 CREDIT RISK DASHBOARD LAUNCHER")
    print("=" * 50)
    
    # Check if setup is needed
    if not os.path.exists("best_model_gradient_boosting.pkl"):
        print("⚠️ Dashboard not set up yet. Running setup...")
        
        # Install requirements
        if not install_requirements():
            return
        
        # Setup dashboard
        if not setup_dashboard():
            return
    else:
        print("✅ Dashboard already set up!")
    
    # Launch dashboard
    print("\n🚀 Starting dashboard...")
    print("📱 Dashboard will open in your browser automatically")
    print("🔗 If not, go to: http://localhost:8501")
    print("⏹️ Press Ctrl+C to stop the dashboard")
    
    launch_streamlit()

if __name__ == "__main__":
    main()
