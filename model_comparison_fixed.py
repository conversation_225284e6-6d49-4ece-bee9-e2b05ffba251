"""
Fixed Model Comparison Script for Credit Risk Prediction
This script resolves the target encoding issue and runs model comparison
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, classification_report
)

# Models
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
# from sklearn.svm import SVC  # Removed: Too slow for large datasets
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier

# Advanced models
try:
    import xgboost as xgb
    print("✅ XGBoost available")
except ImportError:
    print("❌ XGBoost not available")
    xgb = None

try:
    import lightgbm as lgb
    print("✅ LightGBM available")
except ImportError:
    print("❌ LightGBM not available")
    lgb = None

def load_and_prepare_data():
    """Load and prepare data with proper target encoding"""
    print("📂 LOADING AND PREPARING DATA")
    print("=" * 50)
    
    # Load cleaned dataset
    df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
    print(f"✅ Loaded dataset: {df.shape}")
    
    # Get numerical features
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    feature_columns = [col for col in numerical_cols if col not in ['cin']]
    
    # Prepare features and target
    X = df[feature_columns].fillna(0)
    y = df['decision_finale']
    
    print(f"📊 Features: {len(feature_columns)}")
    print(f"🎯 Target classes: {y.unique()}")
    
    # Encode target
    target_encoder = LabelEncoder()
    y_encoded = target_encoder.fit_transform(y)
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
    )
    
    print(f"✅ Train set: {X_train.shape}")
    print(f"✅ Test set: {X_test.shape}")
    
    # Show distribution
    print(f"\n🎯 Target Distribution:")
    for i, class_name in enumerate(target_encoder.classes_):
        count = np.sum(y_encoded == i)
        percentage = (count / len(y_encoded)) * 100
        print(f"   • {class_name}: {count:,} ({percentage:.1f}%)")
    
    return X_train, X_test, y_train, y_test, feature_columns, target_encoder

def setup_models():
    """Setup all models for comparison"""
    print("\n🤖 SETTING UP MODELS")
    print("=" * 50)
    
    models = {}
    
    # Basic models (SVM removed due to slow performance on large datasets)
    models['Logistic Regression'] = LogisticRegression(random_state=42, max_iter=1000, class_weight='balanced')
    models['Random Forest'] = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced')
    models['Extra Trees'] = ExtraTreesClassifier(n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced')
    models['Gradient Boosting'] = GradientBoostingClassifier(n_estimators=100, random_state=42)
    # models['SVM'] = SVC(random_state=42, probability=True, class_weight='balanced')  # REMOVED: Too slow for 100k samples
    models['KNN'] = KNeighborsClassifier(n_neighbors=5, n_jobs=-1)
    models['Naive Bayes'] = GaussianNB()
    
    # Advanced models (if available)
    if xgb is not None:
        models['XGBoost'] = xgb.XGBClassifier(random_state=42, eval_metric='logloss', n_jobs=-1)
    
    if lgb is not None:
        models['LightGBM'] = lgb.LGBMClassifier(random_state=42, n_jobs=-1, verbose=-1)
    
    print(f"✅ Configured {len(models)} models:")
    for i, model_name in enumerate(models.keys(), 1):
        print(f"   {i:2d}. {model_name}")
    
    return models

def train_and_evaluate_models(models, X_train, X_test, y_train, y_test):
    """Train all models and evaluate performance"""
    print("\n🏋️ TRAINING AND EVALUATING MODELS")
    print("=" * 50)

    results = []
    trained_models = {}

    # Estimated training times (for user information)
    estimated_times = {
        'Logistic Regression': '~30 seconds',
        'Random Forest': '~2-3 minutes',
        'Extra Trees': '~2-3 minutes',
        'Gradient Boosting': '~3-5 minutes',
        'XGBoost': '~2-4 minutes',
        'LightGBM': '~1-2 minutes',
        'KNN': '~10 seconds',
        'Naive Bayes': '~5 seconds'
    }

    for model_name, model in models.items():
        estimated_time = estimated_times.get(model_name, '~1-2 minutes')
        print(f"\n🔄 Training {model_name}... (Est. time: {estimated_time})")

        try:
            import time
            start_time = time.time()

            # Train model
            model.fit(X_train, y_train)

            training_time = time.time() - start_time
            print(f"   ⏱️ Training completed in {training_time:.1f} seconds")
            
            # Make predictions
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # ROC-AUC
            if y_pred_proba is not None:
                if len(np.unique(y_test)) > 2:
                    roc_auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')
                else:
                    roc_auc = roc_auc_score(y_test, y_pred_proba[:, 1])
            else:
                roc_auc = np.nan
            
            # Store results
            results.append({
                'Model': model_name,
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'ROC-AUC': roc_auc
            })
            
            trained_models[model_name] = model
            
            print(f"   ✅ {model_name}: F1={f1:.4f}, Acc={accuracy:.4f}")
            
        except Exception as e:
            print(f"   ❌ Error with {model_name}: {str(e)}")
            continue
    
    return results, trained_models

def display_results(results, target_encoder):
    """Display and visualize results"""
    print("\n📊 MODEL COMPARISON RESULTS")
    print("=" * 50)
    
    # Create results dataframe
    results_df = pd.DataFrame(results)
    results_df = results_df.round(4)
    
    # Display table
    print("\n🏆 PERFORMANCE SUMMARY:")
    print(results_df.sort_values('F1-Score', ascending=False).to_string(index=False))
    
    # Best models
    print("\n🥇 BEST MODELS BY METRIC:")
    for metric in ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']:
        if metric in results_df.columns:
            best_idx = results_df[metric].idxmax()
            best_model = results_df.loc[best_idx, 'Model']
            best_score = results_df.loc[best_idx, metric]
            print(f"   • {metric}: {best_model} ({best_score:.4f})")
    
    # Overall ranking
    print("\n🏆 OVERALL RANKING (by F1-Score):")
    ranked = results_df.sort_values('F1-Score', ascending=False)
    for i, (_, row) in enumerate(ranked.iterrows(), 1):
        medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "  "
        print(f"   {medal} {i:2d}. {row['Model']:<20} F1: {row['F1-Score']:.4f}")
    
    # Visualization
    plt.figure(figsize=(12, 8))
    metrics_to_plot = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']
    
    for i, metric in enumerate(metrics_to_plot):
        plt.subplot(2, 3, i+1)
        if metric in results_df.columns:
            sorted_data = results_df.sort_values(metric, ascending=True)
            bars = plt.barh(range(len(sorted_data)), sorted_data[metric])
            plt.yticks(range(len(sorted_data)), sorted_data['Model'])
            plt.xlabel(metric)
            plt.title(f'{metric} Comparison')
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results_df

def main():
    """Main execution function"""
    print("🚀 CREDIT RISK MODEL COMPARISON")
    print("=" * 60)
    
    # Load and prepare data
    X_train, X_test, y_train, y_test, feature_columns, target_encoder = load_and_prepare_data()
    
    # Setup models
    models = setup_models()
    
    # Train and evaluate
    results, trained_models = train_and_evaluate_models(models, X_train, X_test, y_train, y_test)
    
    # Display results
    results_df = display_results(results, target_encoder)
    
    # Save best model
    if len(results) > 0:
        best_model_name = results_df.loc[results_df['F1-Score'].idxmax(), 'Model']
        best_model = trained_models[best_model_name]
        
        print(f"\n💾 SAVING BEST MODEL: {best_model_name}")
        import joblib
        joblib.dump(best_model, f'best_model_{best_model_name.lower().replace(" ", "_")}.pkl')
        
        print(f"✅ Best model saved!")
        print(f"🎯 Recommended model: {best_model_name}")
        print(f"📊 F1-Score: {results_df['F1-Score'].max():.4f}")
    
    print(f"\n🎉 MODEL COMPARISON COMPLETED!")

if __name__ == "__main__":
    main()
