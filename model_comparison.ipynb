{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "title",
   "metadata": {},
   "source": [
    "# Credit Risk Model Comparison & Selection\n",
    "\n",
    "This notebook implements and compares multiple machine learning algorithms to find the best model for credit risk prediction.\n",
    "\n",
    "## 🎯 Objectives:\n",
    "1. **🤖 Model Implementation**: Test multiple ML algorithms (XGBoost, Random Forest, etc.)\n",
    "2. **📊 Performance Comparison**: Evaluate models using comprehensive metrics\n",
    "3. **🔧 Hyperparameter Tuning**: Optimize the best performing models\n",
    "4. **🔍 Model Interpretation**: Analyze feature importance and model behavior\n",
    "5. **🏆 Best Model Selection**: Choose optimal model for deployment\n",
    "\n",
    "## 📋 Models to Compare:\n",
    "- **XGBoost** (Gradient Boosting)\n",
    "- **Random Forest** (Ensemble)\n",
    "- **Logistic Regression** (Linear)\n",
    "- **Support Vector Machine** (SVM)\n",
    "- **LightGBM** (Gradient Boosting)\n",
    "- **CatBoost** (Gradient Boosting)\n",
    "- **Extra Trees** (Ensemble)\n",
    "- **Gradient Boosting** (Scikit-learn)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import necessary libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Machine Learning libraries\n",
    "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV\n",
    "from sklearn.preprocessing import LabelEncoder\n",
    "from sklearn.metrics import (\n",
    "    accuracy_score, precision_score, recall_score, f1_score, \n",
    "    roc_auc_score, confusion_matrix, classification_report,\n",
    "    roc_curve, precision_recall_curve, auc\n",
    ")\n",
    "\n",
    "# Models\n",
    "from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, GradientBoostingClassifier\n",
    "from sklearn.linear_model import LogisticRegression\n",
    "from sklearn.svm import SVC\n",
    "from sklearn.naive_bayes import GaussianNB\n",
    "from sklearn.neighbors import KNeighborsClassifier\n",
    "\n",
    "# Advanced models (install if needed: pip install xgboost lightgbm catboost)\n",
    "try:\n",
    "    import xgboost as xgb\n",
    "    print(\"✅ XGBoost imported\")\n",
    "except ImportError:\n",
    "    print(\"❌ XGBoost not available - install with: pip install xgboost\")\n",
    "    xgb = None\n",
    "\n",
    "try:\n",
    "    import lightgbm as lgb\n",
    "    print(\"✅ LightGBM imported\")\n",
    "except ImportError:\n",
    "    print(\"❌ LightGBM not available - install with: pip install lightgbm\")\n",
    "    lgb = None\n",
    "\n",
    "try:\n",
    "    import catboost as cb\n",
    "    print(\"✅ CatBoost imported\")\n",
    "except ImportError:\n",
    "    print(\"❌ CatBoost not available - install with: pip install catboost\")\n",
    "    cb = None\n",
    "\n",
    "# Visualization and interpretation\n",
    "import plotly.express as px\n",
    "import plotly.graph_objects as go\n",
    "from plotly.subplots import make_subplots\n",
    "\n",
    "# Set style\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"\\n📚 Libraries imported successfully!\")\n",
    "print(\"🎨 Ready for model comparison!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "load-data",
   "metadata": {},
   "source": [
    "## 📂 Load Preprocessed Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "load-preprocessed-data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load preprocessed data\n",
    "print(\"📂 LOADING PREPROCESSED DATA\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "try:\n",
    "    # Load train and test data\n",
    "    train_data = pd.read_pickle('train_data.pkl')\n",
    "    test_data = pd.read_pickle('test_data.pkl')\n",
    "    \n",
    "    # Load preprocessing objects\n",
    "    import pickle\n",
    "    with open('preprocessing_objects.pkl', 'rb') as f:\n",
    "        preprocessing_objects = pickle.load(f)\n",
    "    \n",
    "    feature_columns = preprocessing_objects['feature_columns']\n",
    "    target_encoder = preprocessing_objects['target_encoder']\n",
    "    \n",
    "    print(\"✅ Preprocessed data loaded successfully!\")\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(\"❌ Preprocessed data not found. Please run the EDA & Feature Engineering notebook first.\")\n",
    "    print(\"   Loading and preprocessing data from scratch...\")\n",
    "    \n",
    "    # Fallback: load and preprocess data\n",
    "    df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')\n",
    "    \n",
    "    # Basic preprocessing\n",
    "    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n",
    "    feature_columns = [col for col in numerical_cols if col not in ['cin']]\n",
    "    \n",
    "    X = df[feature_columns].fillna(0)\n",
    "    y = df['decision_finale']\n",
    "    \n",
    "    # Encode target\n",
    "    target_encoder = LabelEncoder()\n",
    "    y_encoded = target_encoder.fit_transform(y)\n",
    "    \n",
    "    # Train-test split\n",
    "    X_train, X_test, y_train, y_test = train_test_split(\n",
    "        X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded\n",
    "    )\n",
    "    \n",
    "    train_data = pd.concat([X_train, pd.Series(y_train, index=X_train.index, name='decision_finale')], axis=1)\n",
    "    test_data = pd.concat([X_test, pd.Series(y_test, index=X_test.index, name='decision_finale')], axis=1)\n",
    "\n",
    "# Prepare final datasets\n",
    "X_train = train_data[feature_columns]\n",
    "y_train_raw = train_data['decision_finale']\n",
    "X_test = test_data[feature_columns]\n",
    "y_test_raw = test_data['decision_finale']\n",
    "\n",
    "# Handle target encoding properly\n",
    "if isinstance(y_train_raw.iloc[0], str):  # If target is still string labels\n",
    "    print(\"   🔄 Encoding target variables...\")\n",
    "    # Create or use existing target encoder\n",
    "    if 'target_encoder' not in locals() or target_encoder is None:\n",
    "        target_encoder = LabelEncoder()\n",
    "        y_train = target_encoder.fit_transform(y_train_raw)\n",
    "        y_test = target_encoder.transform(y_test_raw)\n",
    "    else:\n",
    "        # Check if encoder is compatible\n",
    "        unique_labels = set(y_train_raw.unique()) | set(y_test_raw.unique())\n",
    "        if hasattr(target_encoder, 'classes_') and set(target_encoder.classes_) == unique_labels:\n",
    "            y_train = target_encoder.transform(y_train_raw)\n",
    "            y_test = target_encoder.transform(y_test_raw)\n",
    "        else:\n",
    "            # Re-fit encoder\n",
    "            target_encoder = LabelEncoder()\n",
    "            y_train = target_encoder.fit_transform(y_train_raw)\n",
    "            y_test = target_encoder.transform(y_test_raw)\n",
    "else:  # If target is already encoded\n",
    "    y_train = y_train_raw\n",
    "    y_test = y_test_raw\n",
    "\n",
    "print(f\"\\n📊 Dataset Information:\")\n",
    "print(f\"   Training samples: {len(X_train):,}\")\n",
    "print(f\"   Test samples: {len(X_test):,}\")\n",
    "print(f\"   Features: {len(feature_columns)}\")\n",
    "print(f\"   Classes: {len(np.unique(y_train))}\")\n",
    "\n",
    "# Show class distribution\n",
    "print(f\"\\n🎯 Target Distribution:\")\n",
    "train_dist = pd.Series(y_train).value_counts().sort_index()\n",
    "for class_idx, count in train_dist.items():\n",
    "    try:\n",
    "        if hasattr(target_encoder, 'classes_') and class_idx < len(target_encoder.classes_):\n",
    "            class_name = target_encoder.classes_[class_idx]\n",
    "        else:\n",
    "            class_name = f\"Class_{class_idx}\"\n",
    "    except:\n",
    "        class_name = f\"Class_{class_idx}\"\n",
    "    \n",
    "    percentage = (count / len(y_train)) * 100\n",
    "    print(f\"   • {class_name}: {count:,} ({percentage:.1f}%)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "model-setup",
   "metadata": {},
   "source": [
    "## 🤖 Model Setup & Configuration"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "model-configuration",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define models to compare\n",
    "print(\"🤖 SETTING UP MODELS FOR COMPARISON\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "models = {}\n",
    "\n",
    "# 1. Logistic Regression\n",
    "models['Logistic Regression'] = LogisticRegression(\n",
    "    random_state=42, max_iter=1000, class_weight='balanced'\n",
    ")\n",
    "\n",
    "# 2. Random Forest\n",
    "models['Random Forest'] = RandomForestClassifier(\n",
    "    n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced'\n",
    ")\n",
    "\n",
    "# 3. Extra Trees\n",
    "models['Extra Trees'] = ExtraTreesClassifier(\n",
    "    n_estimators=100, random_state=42, n_jobs=-1, class_weight='balanced'\n",
    ")\n",
    "\n",
    "# 4. Gradient Boosting\n",
    "models['Gradient Boosting'] = GradientBoostingClassifier(\n",
    "    n_estimators=100, random_state=42\n",
    ")\n",
    "\n",
    "# 5. Support Vector Machine\n",
    "models['SVM'] = SVC(\n",
    "    random_state=42, probability=True, class_weight='balanced'\n",
    ")\n",
    "\n",
    "# 6. K-Nearest Neighbors\n",
    "models['KNN'] = KNeighborsClassifier(\n",
    "    n_neighbors=5, n_jobs=-1\n",
    ")\n",
    "\n",
    "# 7. Naive Bayes\n",
    "models['Naive Bayes'] = GaussianNB()\n",
    "\n",
    "# 8. XGBoost (if available)\n",
    "if xgb is not None:\n",
    "    models['XGBoost'] = xgb.XGBClassifier(\n",
    "        random_state=42, eval_metric='logloss', n_jobs=-1\n",
    "    )\n",
    "\n",
    "# 9. LightGBM (if available)\n",
    "if lgb is not None:\n",
    "    models['LightGBM'] = lgb.LGBMClassifier(\n",
    "        random_state=42, n_jobs=-1, verbose=-1\n",
    "    )\n",
    "\n",
    "# 10. CatBoost (if available)\n",
    "if cb is not None:\n",
    "    models['CatBoost'] = cb.CatBoostClassifier(\n",
    "        random_state=42, verbose=False\n",
    "    )\n",
    "\n",
    "print(f\"✅ Configured {len(models)} models for comparison:\")\n",
    "for i, model_name in enumerate(models.keys(), 1):\n",
    "    print(f\"   {i:2d}. {model_name}\")\n",
    "\n",
    "# Define evaluation metrics\n",
    "print(f\"\\n📊 Evaluation Metrics:\")\n",
    "metrics_list = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']\n",
    "for metric in metrics_list:\n",
    "    print(f\"   • {metric}\")"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "model-training",
   "metadata": {},
   "source": [
    "## 🏋️ Model Training & Initial Evaluation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "train-models",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train all models and collect results\n",
    "print(\"🏋️ TRAINING ALL MODELS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "results = []\n",
    "trained_models = {}\n",
    "predictions = {}\n",
    "probabilities = {}\n",
    "\n",
    "for model_name, model in models.items():\n",
    "    print(f\"\\n🔄 Training {model_name}...\")\n",
    "    \n",
    "    try:\n",
    "        # Train the model\n",
    "        model.fit(X_train, y_train)\n",
    "        \n",
    "        # Make predictions\n",
    "        y_pred = model.predict(X_test)\n",
    "        y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None\n",
    "        \n",
    "        # Store predictions\n",
    "        predictions[model_name] = y_pred\n",
    "        probabilities[model_name] = y_pred_proba\n",
    "        trained_models[model_name] = model\n",
    "        \n",
    "        # Calculate metrics\n",
    "        accuracy = accuracy_score(y_test, y_pred)\n",
    "        precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)\n",
    "        recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)\n",
    "        f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)\n",
    "        \n",
    "        # ROC-AUC (for multi-class)\n",
    "        if y_pred_proba is not None and len(np.unique(y_test)) > 2:\n",
    "            roc_auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')\n",
    "        elif y_pred_proba is not None and len(np.unique(y_test)) == 2:\n",
    "            roc_auc = roc_auc_score(y_test, y_pred_proba[:, 1])\n",
    "        else:\n",
    "            roc_auc = np.nan\n",
    "        \n",
    "        # Store results\n",
    "        results.append({\n",
    "            'Model': model_name,\n",
    "            'Accuracy': accuracy,\n",
    "            'Precision': precision,\n",
    "            'Recall': recall,\n",
    "            'F1-Score': f1,\n",
    "            'ROC-AUC': roc_auc\n",
    "        })\n",
    "        \n",
    "        print(f\"   ✅ {model_name} trained successfully!\")\n",
    "        print(f\"      Accuracy: {accuracy:.4f} | F1: {f1:.4f} | ROC-AUC: {roc_auc:.4f}\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"   ❌ Error training {model_name}: {str(e)}\")\n",
    "        continue\n",
    "\n",
    "print(f\"\\n✅ Training completed for {len(results)} models!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "results-comparison",
   "metadata": {},
   "source": [
    "## 📊 Model Performance Comparison"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "compare-results",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create results dataframe\n",
    "results_df = pd.DataFrame(results)\n",
    "results_df = results_df.round(4)\n",
    "\n",
    "print(\"📊 MODEL PERFORMANCE COMPARISON\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Display results table\n",
    "print(\"\\n🏆 PERFORMANCE SUMMARY:\")\n",
    "display(results_df.sort_values('F1-Score', ascending=False))\n",
    "\n",
    "# Find best models for each metric\n",
    "print(\"\\n🥇 BEST MODELS BY METRIC:\")\n",
    "print(\"=\" * 30)\n",
    "for metric in ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']:\n",
    "    if metric in results_df.columns:\n",
    "        best_model = results_df.loc[results_df[metric].idxmax(), 'Model']\n",
    "        best_score = results_df[metric].max()\n",
    "        print(f\"   • {metric}: {best_model} ({best_score:.4f})\")\n",
    "\n",
    "# Create performance visualization\n",
    "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "axes = axes.flatten()\n",
    "\n",
    "metrics_to_plot = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']\n",
    "\n",
    "for i, metric in enumerate(metrics_to_plot):\n",
    "    if i < len(axes) and metric in results_df.columns:\n",
    "        # Sort by metric for better visualization\n",
    "        sorted_data = results_df.sort_values(metric, ascending=True)\n",
    "        \n",
    "        bars = axes[i].barh(range(len(sorted_data)), sorted_data[metric], \n",
    "                           color=plt.cm.viridis(np.linspace(0, 1, len(sorted_data))))\n",
    "        \n",
    "        axes[i].set_yticks(range(len(sorted_data)))\n",
    "        axes[i].set_yticklabels(sorted_data['Model'])\n",
    "        axes[i].set_xlabel(metric)\n",
    "        axes[i].set_title(f'{metric} Comparison', fontweight='bold')\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Add value labels\n",
    "        for j, (bar, value) in enumerate(zip(bars, sorted_data[metric])):\n",
    "            if not np.isnan(value):\n",
    "                axes[i].text(value + 0.01, bar.get_y() + bar.get_height()/2, \n",
    "                           f'{value:.3f}', va='center', fontsize=9)\n",
    "\n",
    "# Hide empty subplot\n",
    "if len(metrics_to_plot) < len(axes):\n",
    "    axes[-1].set_visible(False)\n",
    "\n",
    "plt.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Overall ranking\n",
    "print(\"\\n🏆 OVERALL MODEL RANKING (by F1-Score):\")\n",
    "print(\"=\" * 40)\n",
    "ranked_models = results_df.sort_values('F1-Score', ascending=False)\n",
    "for i, (_, row) in enumerate(ranked_models.iterrows(), 1):\n",
    "    print(f\"   {i:2d}. {row['Model']:<20} (F1: {row['F1-Score']:.4f})\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "detailed-analysis",
   "metadata": {},
   "source": [
    "## 🔍 Detailed Analysis of Top Models"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "top-models-analysis",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Select top 3 models for detailed analysis\n",
    "top_3_models = results_df.nlargest(3, 'F1-Score')['Model'].tolist()\n",
    "\n",
    "print(\"🔍 DETAILED ANALYSIS OF TOP 3 MODELS\")\n",
    "print(\"=\" * 50)\n",
    "print(f\"Top 3 models: {', '.join(top_3_models)}\")\n",
    "\n",
    "# Confusion matrices for top models\n",
    "fig, axes = plt.subplots(1, len(top_3_models), figsize=(6*len(top_3_models), 5))\n",
    "if len(top_3_models) == 1:\n",
    "    axes = [axes]\n",
    "\n",
    "for i, model_name in enumerate(top_3_models):\n",
    "    if model_name in predictions:\n",
    "        cm = confusion_matrix(y_test, predictions[model_name])\n",
    "        \n",
    "        # Create heatmap\n",
    "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i])\n",
    "        axes[i].set_title(f'{model_name}\\nConfusion Matrix', fontweight='bold')\n",
    "        axes[i].set_xlabel('Predicted')\n",
    "        axes[i].set_ylabel('Actual')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Classification reports for top models\n",
    "print(\"\\n📋 CLASSIFICATION REPORTS:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "for model_name in top_3_models:\n",
    "    if model_name in predictions:\n",
    "        print(f\"\\n🔹 {model_name.upper()}:\")\n",
    "        print(\"-\" * 30)\n",
    "        \n",
    "        # Get class names\n",
    "        if hasattr(target_encoder, 'classes_'):\n",
    "            class_names = target_encoder.classes_\n",
    "        else:\n",
    "            class_names = [f\"Class_{i}\" for i in range(len(np.unique(y_test)))]\n",
    "        \n",
    "        report = classification_report(y_test, predictions[model_name], \n",
    "                                     target_names=class_names, \n",
    "                                     zero_division=0)\n",
    "        print(report)"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "hyperparameter-tuning",
   "metadata": {},
   "source": [
    "## 🔧 Hyperparameter Tuning for Best Models"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "tune-hyperparameters",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Hyperparameter tuning for top models\n",
    "print(\"🔧 HYPERPARAMETER TUNING\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Select top 2 models for tuning (to save time)\n",
    "top_models_for_tuning = results_df.nlargest(2, 'F1-Score')['Model'].tolist()\n",
    "print(f\"Tuning hyperparameters for: {', '.join(top_models_for_tuning)}\")\n",
    "\n",
    "tuned_models = {}\n",
    "tuning_results = []\n",
    "\n",
    "# Define parameter grids for different models\n",
    "param_grids = {\n",
    "    'Random Forest': {\n",
    "        'n_estimators': [100, 200, 300],\n",
    "        'max_depth': [10, 20, None],\n",
    "        'min_samples_split': [2, 5, 10],\n",
    "        'min_samples_leaf': [1, 2, 4]\n",
    "    },\n",
    "    'XGBoost': {\n",
    "        'n_estimators': [100, 200, 300],\n",
    "        'max_depth': [3, 6, 10],\n",
    "        'learning_rate': [0.01, 0.1, 0.2],\n",
    "        'subsample': [0.8, 0.9, 1.0]\n",
    "    },\n",
    "    'LightGBM': {\n",
    "        'n_estimators': [100, 200, 300],\n",
    "        'max_depth': [3, 6, 10],\n",
    "        'learning_rate': [0.01, 0.1, 0.2],\n",
    "        'num_leaves': [31, 50, 100]\n",
    "    },\n",
    "    'Logistic Regression': {\n",
    "        'C': [0.1, 1.0, 10.0, 100.0],\n",
    "        'penalty': ['l1', 'l2'],\n",
    "        'solver': ['liblinear', 'saga']\n",
    "    },\n",
    "    'SVM': {\n",
    "        'C': [0.1, 1, 10, 100],\n",
    "        'kernel': ['rbf', 'linear'],\n",
    "        'gamma': ['scale', 'auto', 0.001, 0.01]\n",
    "    }\n",
    "}\n",
    "\n",
    "for model_name in top_models_for_tuning:\n",
    "    if model_name in param_grids and model_name in trained_models:\n",
    "        print(f\"\\n🔄 Tuning {model_name}...\")\n",
    "        \n",
    "        try:\n",
    "            # Get base model\n",
    "            base_model = trained_models[model_name]\n",
    "            \n",
    "            # Perform randomized search (faster than grid search)\n",
    "            random_search = RandomizedSearchCV(\n",
    "                base_model,\n",
    "                param_grids[model_name],\n",
    "                n_iter=20,  # Number of parameter settings sampled\n",
    "                cv=3,       # 3-fold cross-validation\n",
    "                scoring='f1_weighted',\n",
    "                n_jobs=-1,\n",
    "                random_state=42,\n",
    "                verbose=0\n",
    "            )\n",
    "            \n",
    "            # Fit the search\n",
    "            random_search.fit(X_train, y_train)\n",
    "            \n",
    "            # Get best model\n",
    "            best_model = random_search.best_estimator_\n",
    "            tuned_models[model_name] = best_model\n",
    "            \n",
    "            # Evaluate tuned model\n",
    "            y_pred_tuned = best_model.predict(X_test)\n",
    "            y_pred_proba_tuned = best_model.predict_proba(X_test) if hasattr(best_model, 'predict_proba') else None\n",
    "            \n",
    "            # Calculate metrics\n",
    "            accuracy_tuned = accuracy_score(y_test, y_pred_tuned)\n",
    "            f1_tuned = f1_score(y_test, y_pred_tuned, average='weighted')\n",
    "            \n",
    "            # ROC-AUC\n",
    "            if y_pred_proba_tuned is not None and len(np.unique(y_test)) > 2:\n",
    "                roc_auc_tuned = roc_auc_score(y_test, y_pred_proba_tuned, multi_class='ovr', average='weighted')\n",
    "            elif y_pred_proba_tuned is not None and len(np.unique(y_test)) == 2:\n",
    "                roc_auc_tuned = roc_auc_score(y_test, y_pred_proba_tuned[:, 1])\n",
    "            else:\n",
    "                roc_auc_tuned = np.nan\n",
    "            \n",
    "            # Store results\n",
    "            original_f1 = results_df[results_df['Model'] == model_name]['F1-Score'].iloc[0]\n",
    "            improvement = f1_tuned - original_f1\n",
    "            \n",
    "            tuning_results.append({\n",
    "                'Model': f'{model_name} (Tuned)',\n",
    "                'Original_F1': original_f1,\n",
    "                'Tuned_F1': f1_tuned,\n",
    "                'Improvement': improvement,\n",
    "                'Best_Params': random_search.best_params_\n",
    "            })\n",
    "            \n",
    "            print(f\"   ✅ {model_name} tuned successfully!\")\n",
    "            print(f\"      Original F1: {original_f1:.4f} → Tuned F1: {f1_tuned:.4f} (Δ: {improvement:+.4f})\")\n",
    "            print(f\"      Best parameters: {random_search.best_params_}\")\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"   ❌ Error tuning {model_name}: {str(e)}\")\n",
    "            continue\n",
    "\n",
    "# Display tuning results\n",
    "if tuning_results:\n",
    "    print(\"\\n📊 HYPERPARAMETER TUNING RESULTS:\")\n",
    "    print(\"=\" * 50)\n",
    "    tuning_df = pd.DataFrame(tuning_results)\n",
    "    for _, row in tuning_df.iterrows():\n",
    "        print(f\"\\n🔹 {row['Model']}:\")\n",
    "        print(f\"   Original F1: {row['Original_F1']:.4f}\")\n",
    "        print(f\"   Tuned F1: {row['Tuned_F1']:.4f}\")\n",
    "        print(f\"   Improvement: {row['Improvement']:+.4f}\")\n",
    "        print(f\"   Best Params: {row['Best_Params']}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "feature-importance",
   "metadata": {},
   "source": [
    "## 🎯 Feature Importance Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "analyze-feature-importance",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature importance for the best model\n",
    "best_model_name = results_df.loc[results_df['F1-Score'].idxmax(), 'Model']\n",
    "print(f\"🎯 FEATURE IMPORTANCE ANALYSIS - {best_model_name.upper()}\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Get the best model (tuned if available, otherwise original)\n",
    "if best_model_name in tuned_models:\n",
    "    best_model = tuned_models[best_model_name]\n",
    "    print(f\"Using tuned {best_model_name} model\")\n",
    "else:\n",
    "    best_model = trained_models[best_model_name]\n",
    "    print(f\"Using original {best_model_name} model\")\n",
    "\n",
    "# Extract feature importance\n",
    "if hasattr(best_model, 'feature_importances_'):\n",
    "    # Tree-based models\n",
    "    importance_scores = best_model.feature_importances_\n",
    "    importance_type = \"Feature Importance\"\n",
    "elif hasattr(best_model, 'coef_'):\n",
    "    # Linear models\n",
    "    importance_scores = np.abs(best_model.coef_[0]) if len(best_model.coef_.shape) > 1 else np.abs(best_model.coef_)\n",
    "    importance_type = \"Coefficient Magnitude\"\n",
    "else:\n",
    "    print(f\"❌ {best_model_name} doesn't support feature importance extraction\")\n",
    "    importance_scores = None\n",
    "\n",
    "if importance_scores is not None:\n",
    "    # Create feature importance dataframe\n",
    "    feature_importance_df = pd.DataFrame({\n",
    "        'Feature': feature_columns,\n",
    "        'Importance': importance_scores\n",
    "    }).sort_values('Importance', ascending=False)\n",
    "    \n",
    "    # Display top 20 features\n",
    "    print(f\"\\n🏆 TOP 20 FEATURES BY {importance_type.upper()}:\")\n",
    "    print(\"=\" * 50)\n",
    "    for i, (_, row) in enumerate(feature_importance_df.head(20).iterrows(), 1):\n",
    "        print(f\"   {i:2d}. {row['Feature']:<30} {row['Importance']:.4f}\")\n",
    "    \n",
    "    # Visualize feature importance\n",
    "    plt.figure(figsize=(12, 8))\n",
    "    top_features = feature_importance_df.head(20)\n",
    "    \n",
    "    plt.barh(range(len(top_features)), top_features['Importance'],\n",
    "             color=plt.cm.viridis(np.linspace(0, 1, len(top_features))))\n",
    "    plt.yticks(range(len(top_features)), top_features['Feature'])\n",
    "    plt.xlabel(importance_type)\n",
    "    plt.title(f'Top 20 {importance_type} - {best_model_name}', fontweight='bold')\n",
    "    plt.gca().invert_yaxis()\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    # Feature importance statistics\n",
    "    print(f\"\\n📊 FEATURE IMPORTANCE STATISTICS:\")\n",
    "    print(f\"   Total features: {len(feature_importance_df)}\")\n",
    "    print(f\"   Mean importance: {feature_importance_df['Importance'].mean():.4f}\")\n",
    "    print(f\"   Std importance: {feature_importance_df['Importance'].std():.4f}\")\n",
    "    print(f\"   Top 10 features contribute: {feature_importance_df.head(10)['Importance'].sum():.1%} of total importance\")"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "id": "final-selection",
   "metadata": {},
   "source": [
    "## 🏆 Final Model Selection & Recommendations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "final-model-selection",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final model selection and recommendations\n",
    "print(\"🏆 FINAL MODEL SELECTION & RECOMMENDATIONS\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Get the best performing model\n",
    "best_model_name = results_df.loc[results_df['F1-Score'].idxmax(), 'Model']\n",
    "best_f1_score = results_df['F1-Score'].max()\n",
    "best_accuracy = results_df.loc[results_df['F1-Score'].idxmax(), 'Accuracy']\n",
    "best_roc_auc = results_df.loc[results_df['F1-Score'].idxmax(), 'ROC-AUC']\n",
    "\n",
    "print(f\"\\n🥇 RECOMMENDED MODEL: {best_model_name.upper()}\")\n",
    "print(\"=\" * 40)\n",
    "print(f\"   • F1-Score: {best_f1_score:.4f}\")\n",
    "print(f\"   • Accuracy: {best_accuracy:.4f}\")\n",
    "print(f\"   • ROC-AUC: {best_roc_auc:.4f}\")\n",
    "\n",
    "# Check if tuned version is available\n",
    "if best_model_name in tuned_models:\n",
    "    final_model = tuned_models[best_model_name]\n",
    "    print(f\"   • Status: Hyperparameter Tuned ✅\")\n",
    "else:\n",
    "    final_model = trained_models[best_model_name]\n",
    "    print(f\"   • Status: Default Parameters\")\n",
    "\n",
    "# Model comparison summary\n",
    "print(f\"\\n📊 MODEL PERFORMANCE SUMMARY:\")\n",
    "print(\"=\" * 40)\n",
    "summary_stats = results_df[['Accuracy', 'F1-Score', 'ROC-AUC']].describe()\n",
    "display(summary_stats)\n",
    "\n",
    "# Performance ranking\n",
    "print(f\"\\n🏅 FINAL RANKINGS:\")\n",
    "print(\"=\" * 40)\n",
    "ranked_results = results_df.sort_values('F1-Score', ascending=False)\n",
    "for i, (_, row) in enumerate(ranked_results.iterrows(), 1):\n",
    "    medal = \"🥇\" if i == 1 else \"🥈\" if i == 2 else \"🥉\" if i == 3 else \"  \"\n",
    "    print(f\"   {medal} {i:2d}. {row['Model']:<20} F1: {row['F1-Score']:.4f} | Acc: {row['Accuracy']:.4f}\")\n",
    "\n",
    "# Business recommendations\n",
    "print(f\"\\n💼 BUSINESS RECOMMENDATIONS:\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "if best_f1_score >= 0.85:\n",
    "    performance_level = \"Excellent\"\n",
    "    recommendation = \"Ready for production deployment\"\n",
    "elif best_f1_score >= 0.75:\n",
    "    performance_level = \"Good\"\n",
    "    recommendation = \"Suitable for deployment with monitoring\"\n",
    "elif best_f1_score >= 0.65:\n",
    "    performance_level = \"Fair\"\n",
    "    recommendation = \"Consider additional feature engineering or data collection\"\n",
    "else:\n",
    "    performance_level = \"Poor\"\n",
    "    recommendation = \"Requires significant improvement before deployment\"\n",
    "\n",
    "print(f\"   • Performance Level: {performance_level}\")\n",
    "print(f\"   • Recommendation: {recommendation}\")\n",
    "\n",
    "# Model interpretability\n",
    "interpretability_scores = {\n",
    "    'Logistic Regression': 5,\n",
    "    'Random Forest': 4,\n",
    "    'Extra Trees': 4,\n",
    "    'XGBoost': 3,\n",
    "    'LightGBM': 3,\n",
    "    'CatBoost': 3,\n",
    "    'Gradient Boosting': 3,\n",
    "    'SVM': 2,\n",
    "    'KNN': 2,\n",
    "    'Naive Bayes': 4\n",
    "}\n",
    "\n",
    "interpretability = interpretability_scores.get(best_model_name, 3)\n",
    "print(f\"   • Model Interpretability: {interpretability}/5 {'⭐' * interpretability}\")\n",
    "\n",
    "# Training time considerations\n",
    "training_complexity = {\n",
    "    'Logistic Regression': 'Low',\n",
    "    'Random Forest': 'Medium',\n",
    "    'Extra Trees': 'Medium',\n",
    "    'XGBoost': 'High',\n",
    "    'LightGBM': 'Medium',\n",
    "    'CatBoost': 'High',\n",
    "    'Gradient Boosting': 'High',\n",
    "    'SVM': 'High',\n",
    "    'KNN': 'Low',\n",
    "    'Naive Bayes': 'Low'\n",
    "}\n",
    "\n",
    "complexity = training_complexity.get(best_model_name, 'Medium')\n",
    "print(f\"   • Training Complexity: {complexity}\")\n",
    "print(f\"   • Prediction Speed: {'Fast' if complexity == 'Low' else 'Medium' if complexity == 'Medium' else 'Moderate'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "model-saving",
   "metadata": {},
   "source": [
    "## 💾 Save Best Model & Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "save-model",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save the best model and results\n",
    "print(\"💾 SAVING BEST MODEL & RESULTS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "import pickle\n",
    "import joblib\n",
    "from datetime import datetime\n",
    "\n",
    "# Create timestamp for versioning\n",
    "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "\n",
    "# Save the best model\n",
    "model_filename = f'best_credit_model_{best_model_name.lower().replace(\" \", \"_\")}_{timestamp}.pkl'\n",
    "joblib.dump(final_model, model_filename)\n",
    "print(f\"✅ Best model saved: {model_filename}\")\n",
    "\n",
    "# Save model comparison results\n",
    "results_filename = f'model_comparison_results_{timestamp}.csv'\n",
    "results_df.to_csv(results_filename, index=False)\n",
    "print(f\"✅ Results saved: {results_filename}\")\n",
    "\n",
    "# Save feature importance (if available)\n",
    "if 'feature_importance_df' in locals():\n",
    "    importance_filename = f'feature_importance_{timestamp}.csv'\n",
    "    feature_importance_df.to_csv(importance_filename, index=False)\n",
    "    print(f\"✅ Feature importance saved: {importance_filename}\")\n",
    "\n",
    "# Save model metadata\n",
    "model_metadata = {\n",
    "    'best_model_name': best_model_name,\n",
    "    'best_f1_score': best_f1_score,\n",
    "    'best_accuracy': best_accuracy,\n",
    "    'best_roc_auc': best_roc_auc,\n",
    "    'feature_columns': feature_columns,\n",
    "    'target_encoder': target_encoder,\n",
    "    'training_date': timestamp,\n",
    "    'model_type': type(final_model).__name__,\n",
    "    'hyperparameters': final_model.get_params() if hasattr(final_model, 'get_params') else None\n",
    "}\n",
    "\n",
    "metadata_filename = f'model_metadata_{timestamp}.pkl'\n",
    "with open(metadata_filename, 'wb') as f:\n",
    "    pickle.dump(model_metadata, f)\n",
    "print(f\"✅ Model metadata saved: {metadata_filename}\")\n",
    "\n",
    "# Create deployment package\n",
    "deployment_package = {\n",
    "    'model': final_model,\n",
    "    'feature_columns': feature_columns,\n",
    "    'target_encoder': target_encoder,\n",
    "    'preprocessing_objects': preprocessing_objects if 'preprocessing_objects' in locals() else None,\n",
    "    'performance_metrics': {\n",
    "        'f1_score': best_f1_score,\n",
    "        'accuracy': best_accuracy,\n",
    "        'roc_auc': best_roc_auc\n",
    "    }\n",
    "}\n",
    "\n",
    "deployment_filename = f'credit_model_deployment_package_{timestamp}.pkl'\n",
    "with open(deployment_filename, 'wb') as f:\n",
    "    pickle.dump(deployment_package, f)\n",
    "print(f\"✅ Deployment package saved: {deployment_filename}\")\n",
    "\n",
    "print(f\"\\n🎉 MODEL COMPARISON COMPLETED SUCCESSFULLY!\")\n",
    "print(f\"\\n📋 SUMMARY:\")\n",
    "print(f\"   • Best Model: {best_model_name}\")\n",
    "print(f\"   • F1-Score: {best_f1_score:.4f}\")\n",
    "print(f\"   • Models Compared: {len(results)}\")\n",
    "print(f\"   • Files Generated: 4-5 files\")\n",
    "print(f\"\\n🚀 Ready for deployment and production use!\")\n",
    "\n",
    "# Next steps\n",
    "print(f\"\\n📋 NEXT STEPS:\")\n",
    "print(f\"   1. 🔍 Model Validation on new data\")\n",
    "print(f\"   2. 📊 A/B Testing in production\")\n",
    "print(f\"   3. 📈 Performance monitoring\")\n",
    "print(f\"   4. 🔄 Model retraining schedule\")\n",
    "print(f\"   5. 📋 Documentation and handover\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.7"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
