"""
Deployment script for Credit Risk Microservice
Sets up and deploys the FastAPI microservice
"""

import subprocess
import sys
import os
import shutil
import time

def install_requirements():
    """Install required packages"""
    print("📦 Installing microservice requirements...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", "requirements_microservice.txt"
        ])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def prepare_model_files():
    """Prepare model files for deployment"""
    print("🔧 Preparing model files...")
    
    # Check for existing model files
    model_files = [
        'best_model_gradient_boosting.pkl',
        'best_fast_model_gradient_boosting.pkl',
        'dashboard_gradient_boosting_model.pkl'
    ]
    
    model_found = False
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✅ Found model: {model_file}")
            # Copy to standard name for microservice
            if model_file != 'best_model_gradient_boosting.pkl':
                shutil.copy2(model_file, 'best_model_gradient_boosting.pkl')
                print(f"✅ Copied {model_file} to best_model_gradient_boosting.pkl")
            model_found = True
            break
    
    if not model_found:
        print("⚠️ No trained model found. Training a new model...")
        return train_new_model()
    
    # Create target encoder if not exists
    if not os.path.exists('target_encoder.pkl'):
        print("🔧 Creating target encoder...")
        from sklearn.preprocessing import LabelEncoder
        import joblib
        
        target_encoder = LabelEncoder()
        target_encoder.fit(['APPROVE', 'REJECT', 'MANUAL_REVIEW'])
        joblib.dump(target_encoder, 'target_encoder.pkl')
        print("✅ Target encoder created")
    
    return True

def train_new_model():
    """Train a new model if none exists"""
    try:
        print("🤖 Training new Gradient Boosting model...")
        
        import pandas as pd
        import numpy as np
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import GradientBoostingClassifier
        from sklearn.preprocessing import LabelEncoder
        import joblib
        
        # Load data
        df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
        
        # Prepare features
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_columns = [col for col in numerical_cols if col not in ['cin']]
        
        X = df[feature_columns].fillna(0)
        y = df['decision_finale']
        
        # Encode target
        target_encoder = LabelEncoder()
        y_encoded = target_encoder.fit_transform(y)
        
        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        # Train model
        model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        # Save model and encoder
        joblib.dump(model, 'best_model_gradient_boosting.pkl')
        joblib.dump(target_encoder, 'target_encoder.pkl')
        
        print("✅ New model trained and saved successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to train new model: {e}")
        return False

def test_microservice():
    """Test if microservice is running"""
    print("🧪 Testing microservice...")
    try:
        import requests
        time.sleep(2)  # Wait for startup
        
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ Microservice is running and healthy!")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to test microservice: {e}")
        return False

def run_microservice():
    """Run the microservice"""
    print("🚀 Starting Credit Risk Microservice...")
    print("📡 API will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Interactive API: http://localhost:8000/redoc")
    print("⏹️ Press Ctrl+C to stop")
    
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "credit_risk_microservice:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Microservice stopped by user")

def build_docker():
    """Build Docker image"""
    print("🐳 Building Docker image...")
    try:
        subprocess.check_call([
            "docker", "build", "-t", "credit-risk-api", "."
        ])
        print("✅ Docker image built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build Docker image: {e}")
        return False
    except FileNotFoundError:
        print("❌ Docker not found. Please install Docker first.")
        return False

def run_docker():
    """Run Docker container"""
    print("🐳 Running Docker container...")
    try:
        subprocess.run([
            "docker", "run", "-p", "8000:8000", 
            "--name", "credit-risk-api-container",
            "credit-risk-api"
        ])
    except KeyboardInterrupt:
        print("\n👋 Docker container stopped by user")

def run_tests():
    """Run API tests"""
    print("🧪 Running API tests...")
    try:
        subprocess.check_call([sys.executable, "test_microservice.py"])
        return True
    except subprocess.CalledProcessError:
        print("❌ Some tests failed")
        return False

def main():
    """Main deployment function"""
    print("🚀 CREDIT RISK MICROSERVICE DEPLOYMENT")
    print("=" * 60)
    
    # Menu
    print("\nDeployment Options:")
    print("1. 🔧 Setup and run locally")
    print("2. 🐳 Build and run with Docker")
    print("3. 🧪 Run tests only")
    print("4. 📦 Setup only (no run)")
    
    choice = input("\nSelect option (1-4): ").strip()
    
    if choice == "1":
        # Local deployment
        print("\n🔧 LOCAL DEPLOYMENT")
        print("=" * 30)
        
        if not install_requirements():
            return
        
        if not prepare_model_files():
            return
        
        print("\n✅ Setup completed! Starting microservice...")
        run_microservice()
        
    elif choice == "2":
        # Docker deployment
        print("\n🐳 DOCKER DEPLOYMENT")
        print("=" * 30)
        
        if not prepare_model_files():
            return
        
        if not build_docker():
            return
        
        print("\n✅ Docker image ready! Starting container...")
        run_docker()
        
    elif choice == "3":
        # Tests only
        print("\n🧪 RUNNING TESTS")
        print("=" * 30)
        
        print("Make sure the microservice is running first!")
        input("Press Enter when ready...")
        run_tests()
        
    elif choice == "4":
        # Setup only
        print("\n📦 SETUP ONLY")
        print("=" * 30)
        
        if install_requirements() and prepare_model_files():
            print("\n✅ Setup completed!")
            print("\nNext steps:")
            print("• Run locally: python -m uvicorn credit_risk_microservice:app --reload")
            print("• Run tests: python test_microservice.py")
            print("• View docs: http://localhost:8000/docs")
        
    else:
        print("❌ Invalid option selected")

def quick_start():
    """Quick start for immediate deployment"""
    print("⚡ QUICK START DEPLOYMENT")
    print("=" * 30)
    
    steps = [
        ("Installing requirements", install_requirements),
        ("Preparing model files", prepare_model_files),
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ Failed at: {step_name}")
            return False
    
    print("\n✅ Quick setup completed!")
    print("\n🚀 Starting microservice...")
    run_microservice()
    return True

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_start()
    else:
        main()
