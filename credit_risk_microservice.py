"""
Credit Risk Prediction Microservice
FastAPI-based RESTful API for real-time credit risk assessment
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, List
import pandas as pd
import numpy as np
import joblib
import pickle
import logging
from datetime import datetime
import uvicorn
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Credit Risk Prediction API",
    description="Microservice for real-time credit risk assessment using machine learning",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Global variables for model and preprocessing
model = None
feature_columns = None
target_encoder = None
model_metadata = {}

class CreditApplication(BaseModel):
    """Credit application data model with validation"""
    
    # Personal Information
    age: int = Field(..., ge=18, le=100, description="Applicant's age in years")
    nombre_enfants: int = Field(0, ge=0, le=10, description="Number of dependent children")
    
    # Financial Information
    revenu_mensuel: float = Field(..., gt=0, le=50000, description="Monthly income in TND")
    montant_demande: float = Field(..., gt=0, le=1000000, description="Requested loan amount in TND")
    duree_demande: int = Field(..., gt=0, le=360, description="Requested loan duration in months")
    dette_totale: float = Field(0, ge=0, le=500000, description="Total existing debt in TND")
    
    # Employment
    anciennete_emploi: int = Field(0, ge=0, le=50, description="Employment tenure in years")
    
    # Assets
    patrimoine_total: float = Field(0, ge=0, le=2000000, description="Total assets value in TND")
    valeur_immobilier: float = Field(0, ge=0, le=1500000, description="Real estate value in TND")
    valeur_vehicule: float = Field(0, ge=0, le=200000, description="Vehicle value in TND")
    epargne: float = Field(0, ge=0, le=500000, description="Savings amount in TND")
    
    # Credit History
    score_comportement: float = Field(0.5, ge=0, le=1, description="Behavioral credit score (0-1)")
    nombre_credits_anterieurs: int = Field(0, ge=0, le=20, description="Number of previous credits")
    retard_maximum_jours: int = Field(0, ge=0, le=365, description="Maximum payment delay in days")
    nombre_incidents_12m: int = Field(0, ge=0, le=50, description="Payment incidents in last 12 months")
    nombre_rejets_12m: int = Field(0, ge=0, le=20, description="Credit rejections in last 12 months")
    
    # Banking
    anciennete_relation_bancaire: int = Field(0, ge=0, le=50, description="Banking relationship duration in years")
    
    # Optional fields with defaults
    autres_revenus: Optional[float] = Field(0, ge=0, le=20000, description="Other income sources in TND")
    apport_personnel: Optional[float] = Field(0, ge=0, le=500000, description="Personal contribution in TND")
    taux_chomage_sectoriel: Optional[float] = Field(0.1, ge=0, le=1, description="Sectoral unemployment rate")
    
    @validator('montant_demande')
    def validate_loan_amount(cls, v, values):
        if 'revenu_mensuel' in values and v > values['revenu_mensuel'] * 120:  # Max 10 years of income
            raise ValueError('Loan amount too high relative to income')
        return v
    
    @validator('dette_totale')
    def validate_debt(cls, v, values):
        if 'revenu_mensuel' in values and v > values['revenu_mensuel'] * 60:  # Max 5 years of income
            raise ValueError('Total debt too high relative to income')
        return v

class PredictionResponse(BaseModel):
    """Prediction response model"""
    decision: str = Field(..., description="Credit decision: APPROVE, REJECT, or MANUAL_REVIEW")
    confidence: float = Field(..., description="Prediction confidence (0-1)")
    probabilities: Dict[str, float] = Field(..., description="Probability for each decision class")
    risk_score: float = Field(..., description="Overall risk score (0-1, higher = riskier)")
    recommendation: str = Field(..., description="Business recommendation")
    timestamp: datetime = Field(default_factory=datetime.now, description="Prediction timestamp")

class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    model_loaded: bool
    model_version: str
    timestamp: datetime

@app.on_event("startup")
async def load_model():
    """Load ML model and preprocessing objects on startup"""
    global model, feature_columns, target_encoder, model_metadata
    
    try:
        logger.info("Loading ML model and preprocessing objects...")
        
        # Load model
        model_files = [
            'best_model_gradient_boosting.pkl',
            'best_fast_model_gradient_boosting.pkl',
            'dashboard_gradient_boosting_model.pkl'
        ]
        
        for model_file in model_files:
            if os.path.exists(model_file):
                model = joblib.load(model_file)
                logger.info(f"Model loaded from: {model_file}")
                break
        
        if model is None:
            raise FileNotFoundError("No trained model found")
        
        # Load dataset to get feature information
        if os.path.exists('tunisian_credit_dataset_cleaned.pkl'):
            df = pd.read_pickle('tunisian_credit_dataset_cleaned.pkl')
            numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            feature_columns = [col for col in numerical_cols if col not in ['cin']]
        else:
            # Fallback feature list if dataset not available
            feature_columns = [
                'age', 'nombre_enfants', 'revenu_mensuel', 'montant_demande', 'duree_demande',
                'dette_totale', 'anciennete_emploi', 'patrimoine_total', 'valeur_immobilier',
                'valeur_vehicule', 'epargne', 'score_comportement', 'nombre_credits_anterieurs',
                'retard_maximum_jours', 'nombre_incidents_12m', 'nombre_rejets_12m',
                'anciennete_relation_bancaire', 'autres_revenus', 'revenu_total',
                'ratio_endettement', 'ratio_mensualite_revenu', 'reste_a_vivre',
                'capacite_remboursement', 'apport_personnel', 'taux_chomage_sectoriel'
            ]

        # Load or create target encoder
        target_encoder = joblib.load('target_encoder.pkl') if os.path.exists('target_encoder.pkl') else None
        if target_encoder is None:
            from sklearn.preprocessing import LabelEncoder
            target_encoder = LabelEncoder()
            target_encoder.fit(['APPROVE', 'REJECT', 'MANUAL_REVIEW'])
            joblib.dump(target_encoder, 'target_encoder.pkl')
        
        # Model metadata
        model_metadata = {
            "model_type": type(model).__name__,
            "features_count": len(feature_columns),
            "target_classes": target_encoder.classes_.tolist(),
            "loaded_at": datetime.now().isoformat()
        }
        
        logger.info("Model and preprocessing objects loaded successfully")
        logger.info(f"Model type: {model_metadata['model_type']}")
        logger.info(f"Features: {model_metadata['features_count']}")
        
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        raise

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Simple token verification (implement proper auth in production)"""
    # For demo purposes - in production, implement proper JWT validation
    if credentials.credentials != "demo-token-123":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token"
        )
    return credentials.credentials

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Credit Risk Prediction Microservice",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy" if model is not None else "unhealthy",
        model_loaded=model is not None,
        model_version=model_metadata.get("model_type", "unknown"),
        timestamp=datetime.now()
    )

@app.get("/model/info")
async def model_info():
    """Get model information and metadata"""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    return {
        "model_metadata": model_metadata,
        "feature_columns": feature_columns,
        "target_classes": target_encoder.classes_.tolist() if target_encoder else []
    }

def prepare_features(application: CreditApplication) -> pd.DataFrame:
    """Prepare features for model prediction"""
    # Convert application to dictionary
    app_dict = application.dict()
    
    # Calculate derived features
    app_dict['revenu_total'] = app_dict['revenu_mensuel'] + app_dict.get('autres_revenus', 0)
    
    # Calculate ratios
    if app_dict['revenu_mensuel'] > 0:
        app_dict['ratio_endettement'] = app_dict['dette_totale'] / (app_dict['revenu_mensuel'] * 12)
        app_dict['ratio_mensualite_revenu'] = (app_dict['montant_demande'] / app_dict['duree_demande']) / app_dict['revenu_mensuel']
    else:
        app_dict['ratio_endettement'] = 0
        app_dict['ratio_mensualite_revenu'] = 0
    
    # Calculate remaining features with defaults
    app_dict['reste_a_vivre'] = max(0, app_dict['revenu_mensuel'] - (app_dict['dette_totale'] / 12))
    app_dict['capacite_remboursement'] = app_dict['reste_a_vivre'] * 0.3  # 30% of disposable income
    
    # Create DataFrame with all required features
    feature_dict = {}
    for col in feature_columns:
        if col in app_dict:
            feature_dict[col] = app_dict[col]
        else:
            feature_dict[col] = 0  # Default value for missing features
    
    return pd.DataFrame([feature_dict])

@app.post("/predict", response_model=PredictionResponse)
async def predict_credit_risk(
    application: CreditApplication,
    token: str = Depends(verify_token)
):
    """
    Predict credit risk for a loan application
    
    Returns the credit decision (APPROVE/REJECT/MANUAL_REVIEW) with confidence scores
    """
    try:
        if model is None:
            raise HTTPException(status_code=503, detail="Model not available")
        
        logger.info(f"Processing credit prediction for application")
        
        # Prepare features
        features_df = prepare_features(application)
        
        # Make prediction
        prediction = model.predict(features_df)[0]
        prediction_proba = model.predict_proba(features_df)[0]
        
        # Get decision and probabilities
        decision = target_encoder.classes_[prediction]
        probabilities = {
            target_encoder.classes_[i]: float(prob) 
            for i, prob in enumerate(prediction_proba)
        }
        
        # Calculate confidence and risk score
        confidence = float(max(prediction_proba))
        risk_score = 1.0 - probabilities.get('APPROVE', 0.0)  # Higher = riskier
        
        # Generate recommendation
        if decision == 'APPROVE' and confidence > 0.8:
            recommendation = "Strong approval candidate - proceed with standard terms"
        elif decision == 'APPROVE':
            recommendation = "Approval recommended with enhanced monitoring"
        elif decision == 'REJECT' and confidence > 0.8:
            recommendation = "High risk - recommend rejection"
        elif decision == 'REJECT':
            recommendation = "Elevated risk - consider rejection or manual review"
        else:
            recommendation = "Borderline case - requires manual underwriting review"
        
        response = PredictionResponse(
            decision=decision,
            confidence=confidence,
            probabilities=probabilities,
            risk_score=risk_score,
            recommendation=recommendation
        )
        
        logger.info(f"Prediction completed: {decision} (confidence: {confidence:.3f})")
        return response
        
    except Exception as e:
        logger.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/batch-predict")
async def batch_predict(
    applications: List[CreditApplication],
    token: str = Depends(verify_token)
):
    """
    Batch prediction for multiple credit applications
    """
    if len(applications) > 100:
        raise HTTPException(status_code=400, detail="Maximum 100 applications per batch")
    
    results = []
    for i, app in enumerate(applications):
        try:
            prediction = await predict_credit_risk(app, token)
            results.append({"application_id": i, "prediction": prediction})
        except Exception as e:
            results.append({"application_id": i, "error": str(e)})
    
    return {"batch_results": results, "total_processed": len(results)}

if __name__ == "__main__":
    uvicorn.run(
        "credit_risk_microservice:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
